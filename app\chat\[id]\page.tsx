"use client";

import { useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { ChatArea } from "@/app/chat/components/chat-area";
import { useConversation } from "@/app/chat/context/ConversationContext";

export default function ChatConversationPage() {
  const router = useRouter();
  const params = useParams();
  const { setActiveConversationId } = useConversation();

  const conversationId = params.id as string;

  useEffect(() => {
    const accessToken = document.cookie
      .split("; ")
      .find((row) => row.startsWith("access_token="))
      ?.split("=")[1];

    if (!accessToken) {
      router.push("/login");
    }
  }, [router]);

  // 根据URL参数设置活跃会话
  useEffect(() => {
    if (conversationId) {
      setActiveConversationId(conversationId);
    }
  }, [conversationId, setActiveConversationId]);

  return (
    <div className="h-full w-full">
      <ChatArea />
    </div>
  );
}
