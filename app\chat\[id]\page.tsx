"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { useTranslation } from "@/i18n";
import { ChatArea } from "@/app/chat/components/chat-area";
import { RightPanel } from "@/app/chat/components/right-panel";
import { ResizeHandle } from "@/app/chat/components/resize-handle";
import { useConversation } from "@/app/chat/context/ConversationContext";
import {
  RightPanelState,
  RightPanelType,
  RightPanelData,
} from "@/app/chat/types/right-panel";
import { motion } from "framer-motion";

export default function ChatConversationPage() {
  const router = useRouter();
  const params = useParams();
  const { setActiveConversationId } = useConversation();
  const [rightPanelWidth, setRightPanelWidth] = useState<number>(0); // 百分比，默认隐藏
  const [isClient, setIsClient] = useState(false);
  const [isResizing, setIsResizing] = useState(false);

  // 右侧面板状态管理
  const [rightPanelState, setRightPanelState] = useState<RightPanelState>({
    visible: false,
    type: "none",
    data: null,
  });

  const conversationId = params.id as string;

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    const accessToken = document.cookie
      .split("; ")
      .find((row) => row.startsWith("access_token="))
      ?.split("=")[1];

    if (!accessToken) {
      router.push("/login");
    }
  }, [router]);

  // 根据URL参数设置活跃会话
  useEffect(() => {
    if (conversationId) {
      setActiveConversationId(conversationId);
    }
  }, [conversationId, setActiveConversationId]);

  const handleRightPanelResize = (delta: number) => {
    setRightPanelWidth((prev) => {
      if (!isClient) return prev;
      // 根据拖拽距离调整百分比
      const deltaPercent = (delta / window.innerWidth) * 100;
      const newPercent = prev - deltaPercent;
      // 限制右侧面板在 20% 到 70% 之间，确保左侧至少有 30% 宽度
      return Math.max(20, Math.min(70, newPercent));
    });
  };

  // 右侧面板控制函数
  const openRightPanel = (type: RightPanelType, data: any) => {
    setRightPanelState({
      visible: true,
      type,
      data: { type, data },
    });
    // 打开面板时设置合适的宽度
    setRightPanelWidth(50);
  };

  const closeRightPanel = () => {
    setRightPanelState({
      visible: false,
      type: "none",
      data: null,
    });
    // 关闭面板时重置宽度为0
    setRightPanelWidth(0);
  };

  const toggleRightPanel = () => {
    setRightPanelState((prev) => ({
      ...prev,
      visible: !prev.visible,
    }));
  };

  return (
    <div className="flex h-full w-full overflow-hidden">
      <motion.div
        key="chat-area"
        initial={{ width: "100%" }}
        animate={{
          width: rightPanelState.visible ? `${100 - rightPanelWidth}%` : "100%",
        }}
        transition={{
          type: "tween",
          ease: "easeOut",
          duration: isResizing ? 0 : 0.25,
        }}
        style={{ minWidth: "30%" }}
        className="min-w-0"
      >
        <ChatArea onOpenRightPanel={openRightPanel} />
      </motion.div>

      {/* 拖拽条常驻：仅根据可见性切换透明度与指针，避免挂载延迟造成空白 */}
      <motion.div
        key="resize-handle"
        initial={false}
        animate={{ opacity: rightPanelState.visible ? 1 : 0 }}
        transition={{ duration: 0.2 }}
        className="flex-shrink-0 h-full relative z-20"
        style={{ pointerEvents: rightPanelState.visible ? "auto" : "none" }}
      >
        <ResizeHandle
          direction="horizontal"
          onResize={handleRightPanelResize}
          className="flex-shrink-0 h-full bg-sidebar-border"
          onDragChange={setIsResizing}
        />
      </motion.div>

      {/* 右侧容器常驻：宽度在 0% 与目标值之间过渡，确保总宽度恒为 100% */}
      <motion.div
        key="right-group"
        className="flex"
        initial={false}
        animate={{
          width: rightPanelState.visible ? `${rightPanelWidth}%` : "0%",
        }}
        transition={{
          type: "tween",
          ease: "easeOut",
          duration: isResizing ? 0 : 0.25,
        }}
        style={{ overflow: "hidden" }}
      >
        <div className="w-full h-full overflow-hidden min-w-0">
          <RightPanel state={rightPanelState} onClose={closeRightPanel} />
        </div>
      </motion.div>
    </div>
  );
}
