'use client';

import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Trash2, AlertTriangle } from 'lucide-react';
import { motion } from 'framer-motion';
import * as Dialog from '@/components/ui/dialog';
import { Conversation } from './sidebar';

interface DeleteConversationModalProps {
  conversation: Conversation;
  onDelete: (id: string) => Promise<void>;
}

export function DeleteConversationModal({ conversation, onDelete }: DeleteConversationModalProps) {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleDelete = async () => {
    setIsLoading(true);
    try {
      await onDelete(conversation.id);
      setIsOpen(false);
    } catch (error) {
      console.error('删除会话失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog.Dialog open={isOpen} onOpenChange={setIsOpen}>
      <Dialog.DialogTrigger asChild>
        <motion.button
          className="p-1.5 rounded-md hover:bg-destructive/10 hover:text-destructive text-foreground/70 transition-colors"
          aria-label={t('chat.deleteConversation')}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <Trash2 className="h-3.5 w-3.5" />
        </motion.button>
      </Dialog.DialogTrigger>
      
      <Dialog.DialogContent className="sm:max-w-md">
        <Dialog.DialogHeader>
          <Dialog.DialogTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            {t('chat.deleteConversation')}
          </Dialog.DialogTitle>
          <Dialog.DialogDescription>
            {t('chat.deleteConversationConfirm', '确定要删除这个会话吗？此操作无法撤销。')}
          </Dialog.DialogDescription>
        </Dialog.DialogHeader>
        
        <div className="py-4">
          <div className="p-3 bg-muted/30 rounded-lg border border-border/50">
            <div className="font-medium text-sm truncate">
              {conversation.title}
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              {new Date(conversation.created_at).toLocaleString('zh-CN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>
          </div>
        </div>

        <Dialog.DialogFooter>
          <Dialog.DialogClose asChild>
            <motion.button
              className="px-4 py-2 text-sm font-medium text-muted-foreground hover:text-foreground bg-background border border-border rounded-lg hover:bg-accent/50 transition-colors"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {t('common.cancel')}
            </motion.button>
          </Dialog.DialogClose>
          <motion.button
            onClick={handleDelete}
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium bg-destructive text-destructive-foreground rounded-lg hover:bg-destructive/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {isLoading ? t('common.deleting', '删除中...') : t('common.delete')}
          </motion.button>
        </Dialog.DialogFooter>
      </Dialog.DialogContent>
    </Dialog.Dialog>
  );
}