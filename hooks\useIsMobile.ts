import { useEffect, useState } from "react";

/**
 * 统一的移动端判断 Hook
 * - 默认断点为 768（与 Tailwind md 保持一致）
 * - 在 SSR/初次渲染时默认返回 false，避免水合不一致
 */
export default function useIsMobile(breakpoint: number = 768) {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIfMobile = () => {
      // 防御：window 仅在浏览器环境存在
      if (typeof window !== "undefined") {
        setIsMobile(window.innerWidth < breakpoint);
      }
    };

    checkIfMobile();
    window.addEventListener("resize", checkIfMobile);
    return () => {
      window.removeEventListener("resize", checkIfMobile);
    };
  }, [breakpoint]);

  return isMobile;
}
