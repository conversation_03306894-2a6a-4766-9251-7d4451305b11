"use client";

import { Sidebar } from "./components/sidebar";
import { ConversationProvider } from "./context/ConversationContext";
import { UIProvider, useUI } from "./context/UIContext";
import { ChatHeader } from "./components/chat-header";
import { usePathname } from "next/navigation";
import { motion } from 'framer-motion';
import { RightPanel } from "./components/right-panel";

export default function LayoutChat({
  children,
}: {
  children: React.ReactNode;
}) {
  const Inner = () => {
    const { sidebarVisible } = useUI();
    const pathname = usePathname();
    const isNewPage = pathname?.startsWith("/chat/new");
    return (
      <main className="w-screen h-screen flex overflow-hidden relative">
        {/* 桌面端左侧边栏（保持原有行为） */}
        <div className="hidden md:block">
          <motion.div
            initial={false}
            animate={{ width: sidebarVisible ? 256 : 0 }}
            transition={{ duration: 0.25, ease: 'easeOut' }}
            style={{ overflow: 'hidden' }}
            className="h-full flex-shrink-0"
            aria-hidden={!sidebarVisible}
          >
            <div className="w-64 h-full">
              <Sidebar />
            </div>
          </motion.div>
        </div>

        {/* 移动端左侧边栏（浮层遮罩 + 左侧滑入） */}
        <MobileSidebarOverlay />

        {/* 主内容区 */}
        <div className="flex-1 min-w-0 min-h-0 flex flex-col">
          <ChatHeader showTitle={!isNewPage} disableNew={!!isNewPage} />
          <div className="flex-1 min-h-0 min-w-0">
            {children}
          </div>
        </div>

        {/* 右侧面板：桌面从右侧滑入，移动端自下而上展开 */}
        <RightPanelOverlay />
      </main>
    );
  };

  return (
    <ConversationProvider>
      <UIProvider>
        <Inner />
      </UIProvider>
    </ConversationProvider>
  );
}

function MobileSidebarOverlay() {
  const { sidebarVisible, toggleSidebar } = useUI();
  return (
    <div className="md:hidden">
      {/* 背景遮罩 */}
      {sidebarVisible && (
        <motion.div
          key="backdrop"
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.4 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 bg-black z-30"
          onClick={toggleSidebar}
          aria-label="关闭侧边栏"
        />
      )}

      {/* 左侧抽屉 */}
      <motion.aside
        key="mobile-sidebar"
        initial={false}
        animate={{ x: sidebarVisible ? 0 : -320 }}
        transition={{ duration: 0.25, ease: 'easeOut' }}
        className="fixed left-0 top-0 bottom-0 z-40 w-[80vw] max-w-[320px] bg-sidebar md:border-r border-sidebar-border shadow-xl overflow-hidden"
        style={{ transform: sidebarVisible ? 'translateX(0)' : 'translateX(-320px)' }}
        aria-hidden={!sidebarVisible}
      >
        <Sidebar />
      </motion.aside>
    </div>
  );
}

function RightPanelOverlay() {
  const { rightPanelOpen, rightPanelState, closeRightPanel } = useUI();

  return (
    <>
      {rightPanelOpen && (
        <motion.div
          key="rp-backdrop"
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.4 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 bg-black/80 z-30 md:bg-black/40"
          onClick={closeRightPanel}
          aria-label="关闭右侧面板"
        />
      )}

      <motion.aside
        key="rp-mobile"
        initial={false}
        animate={{ y: rightPanelOpen ? 0 : "110%" }}
        transition={{ duration: 0.25, ease: 'easeOut' }}
        className="md:hidden fixed left-0 right-0 bottom-0 z-40 h-[65vh] max-h-[80vh] bg-background border-t border-border rounded-t-2xl shadow-2xl"
        style={{ transform: rightPanelOpen ? 'translateY(0)' : 'translateY(110%)', pointerEvents: rightPanelOpen ? 'auto' : 'none', visibility: rightPanelOpen ? 'visible' : 'hidden' }}
        aria-hidden={!rightPanelOpen}
      >
        <RightPanel state={rightPanelState} onClose={closeRightPanel} />
      </motion.aside>
    </>
  );
}
