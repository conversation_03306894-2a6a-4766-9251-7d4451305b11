"use client";

import React, { createContext, useContext, useState, useMemo, ReactNode, useEffect } from "react";
import type { RightPanelData, RightPanelState, RightPanelType } from "@/app/chat/types/right-panel";
import useIsMobile from "@/hooks/useIsMobile";

interface UIState {
  sidebarVisible: boolean;
  setSidebarVisible: (v: boolean) => void;
  toggleSidebar: () => void;

  rightPanelOpen: boolean;
  rightPanelState: RightPanelState;
  openRightPanel: (type?: RightPanelType, data?: any) => void;
  closeRightPanel: () => void;
  toggleRightPanel: () => void;
}

const UIContext = createContext<UIState | null>(null);

export function UIProvider({ children }: { children: ReactNode }) {
  const [sidebarVisible, setSidebarVisible] = useState(true);
  const [rightPanelOpen, setRightPanelOpen] = useState(false);
  const [rightPanelState, setRightPanelState] = useState<RightPanelState>({
    visible: false,
    type: 'none',
    data: null,
  });
  const isMobile = useIsMobile();

  // 根据断点设置侧边栏可见性：移动端默认收起，桌面端默认展开
  useEffect(() => {
    setSidebarVisible(!isMobile);
  }, [isMobile]);

  const value = useMemo<UIState>(
    () => ({
      sidebarVisible,
      setSidebarVisible,
      toggleSidebar: () => setSidebarVisible((v) => !v),

      rightPanelOpen,
      rightPanelState,
      openRightPanel: (type, data) => {
        // 当传入类型与数据时，同时设置面板内容状态
        if (type) {
          const rpData: RightPanelData = { type, data } as any;
          setRightPanelState({ visible: true, type, data: rpData });
        }
        setRightPanelOpen(true);
      },
      closeRightPanel: () => {
        setRightPanelOpen(false);
        setRightPanelState({ visible: false, type: 'none', data: null });
      },
      toggleRightPanel: () => setRightPanelOpen((v) => !v),
    }),
    [sidebarVisible, rightPanelOpen, rightPanelState]
  );

  return <UIContext.Provider value={value}>{children}</UIContext.Provider>;
}

export function useUI() {
  const ctx = useContext(UIContext);
  if (!ctx) throw new Error("useUI must be used within UIProvider");
  return ctx;
}
