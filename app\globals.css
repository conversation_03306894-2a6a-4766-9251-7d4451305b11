@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-quicksand), var(--font-zcool), system-ui, sans-serif;
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* Blue-purple theme for light mode */
  --background: oklch(99% 0.01 260); /* Very light blue-purple */
  --foreground: oklch(15% 0.05 260); /* Dark blue-purple */
  --card: oklch(100% 0 0); /* White */
  --card-foreground: oklch(15% 0 0); /* Near black */
  --popover: oklch(100% 0 0); /* White */
  --popover-foreground: oklch(15% 0 0); /* Near black */
  --primary: oklch(60% 0.2 280); /* Blue-purple */
  --primary-foreground: oklch(98% 0 0); /* White */
  --secondary: oklch(90% 0.1 280); /* Light blue-purple */
  --secondary-foreground: oklch(15% 0.05 280); /* Dark blue-purple */
  --muted: oklch(93% 0.02 280); /* Very light blue-purple */
  --muted-foreground: oklch(50% 0.05 280); /* Medium blue-purple */
  --accent: oklch(80% 0.15 290); /* Purple */
  --accent-foreground: oklch(98% 0 0); /* White */
  --destructive: oklch(65% 0.2 25); /* Red */
  --destructive-foreground: oklch(98% 0 0); /* White */
  --border: oklch(90% 0.02 280); /* Light blue-purple */
  --input: oklch(90% 0.02 280); /* Light blue-purple */
  --ring: oklch(60% 0.2 280); /* Blue-purple */
  --chart-1: oklch(65% 0.2 25);
  --chart-2: oklch(70% 0.15 60);
  --chart-3: oklch(65% 0.15 100);
  --chart-4: oklch(65% 0.15 180);
  --chart-5: oklch(65% 0.15 280);
  --sidebar: oklch(98% 0.01 260); /* Very light blue-purple */
  --sidebar-foreground: oklch(15% 0.02 260); /* Dark blue-purple */
  --sidebar-primary: oklch(60% 0.2 280); /* Blue-purple */
  --sidebar-primary-foreground: oklch(98% 0 0); /* White */
  --sidebar-accent: oklch(80% 0.15 290); /* Purple */
  --sidebar-accent-foreground: oklch(98% 0 0); /* White */
  --sidebar-border: oklch(90% 0.02 280); /* Light blue-purple */
  --sidebar-ring: oklch(60% 0.2 280); /* Blue-purple */
}

.dark {
  /* Blue-purple theme for dark mode */
  --background: oklch(15% 0.05 260); /* Dark blue-purple */
  --foreground: oklch(90% 0.1 260); /* Light blue-purple */
  --card: oklch(20% 0.05 260); /* Slightly lighter dark blue-purple */
  --card-foreground: oklch(90% 0.1 260); /* Light blue-purple */
  --popover: oklch(20% 0.05 260); /* Slightly lighter dark blue-purple */
  --popover-foreground: oklch(90% 0.1 260); /* Light blue-purple */
  --primary: oklch(60% 0.2 280); /* Blue-purple */
  --primary-foreground: oklch(98% 0 0); /* White */
  --secondary: oklch(30% 0.1 280); /* Medium dark blue-purple */
  --secondary-foreground: oklch(90% 0.1 280); /* Light blue-purple */
  --muted: oklch(25% 0.05 280); /* Dark blue-purple */
  --muted-foreground: oklch(60% 0.1 280); /* Medium light blue-purple */
  --accent: oklch(50% 0.2 290); /* Purple */
  --accent-foreground: oklch(98% 0 0); /* White */
  --destructive: oklch(65% 0.2 25); /* Red */
  --destructive-foreground: oklch(98% 0 0); /* White */
  --border: oklch(30% 0.05 280); /* Medium dark blue-purple */
  --input: oklch(30% 0.05 280); /* Medium dark blue-purple */
  --ring: oklch(60% 0.2 280); /* Blue-purple */
  --chart-1: oklch(65% 0.2 25);
  --chart-2: oklch(70% 0.15 60);
  --chart-3: oklch(65% 0.15 100);
  --chart-4: oklch(65% 0.15 180);
  --chart-5: oklch(65% 0.15 280);
  --sidebar: oklch(20% 0.05 260); /* Dark blue-purple */
  --sidebar-foreground: oklch(90% 0.1 260); /* Light blue-purple */
  --sidebar-primary: oklch(60% 0.2 280); /* Blue-purple */
  --sidebar-primary-foreground: oklch(98% 0 0); /* White */
  --sidebar-accent: oklch(50% 0.2 290); /* Purple */
  --sidebar-accent-foreground: oklch(98% 0 0); /* White */
  --sidebar-border: oklch(30% 0.05 280); /* Medium dark blue-purple */
  --sidebar-ring: oklch(60% 0.2 280); /* Blue-purple */
}

@layer base {
  * {
    @apply border-border;
    overscroll-behavior-x: none;
  }

  /* 隐藏滚动条但保留滚动功能的工具类 */
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  body {
    @apply bg-background text-foreground;
    overscroll-behavior-x: none;
    overscroll-behavior-y: none;
    min-height: 100vh;
    /* Prevent layout shifts when scrollbar appears/disappears */
    scrollbar-gutter: stable;
    /* Improve animation performance */
    text-rendering: optimizeSpeed;
  }

  ::-webkit-scrollbar {
    width: var(--scrollbar-size);
    height: var(--scrollbar-size);
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground) / 0.3);
    border-radius: var(--radius);
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground) / 0.3);
  }

  /* For Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
  }

  :root {
    --radius: 0.625rem;
  }
  .dark {
    --radius: 0.625rem;
  }

  button {
    cursor: pointer;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-enter {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
}

/* 为流式输出的字符添加动画 */
@keyframes charFadeIn {
  from {
    opacity: 0;
    transform: translateY(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.char-animate {
  display: inline-block;
  animation: charFadeIn 0.1s ease-out forwards;
  opacity: 0;
}
