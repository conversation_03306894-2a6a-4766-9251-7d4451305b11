"use client";

import React from "react";
import {
  Popover,
  Popover<PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ExternalLink } from "lucide-react";

// 引用数据类型定义
export interface CitationData {
  context: string;
  favicon?: string;
  order: number;
  title: string;
  url: string;
}

interface CitationProps {
  number: number;
  data?: CitationData;
  className?: string;
}

export function Citation({ number, data, className = "" }: CitationProps) {
  if (!data) {
    // 如果没有引用数据，只显示数字
    return (
      <span
        className={`inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-muted-foreground bg-muted rounded-full ${className}`}
      >
        {number}
      </span>
    );
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <button
          className={`inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-primary bg-primary/10 hover:bg-primary/20 rounded-full transition-colors cursor-pointer ${className}`}
          aria-label={`引用 ${number}: ${data.title}`}
        >
          {number + 1}
        </button>
      </PopoverTrigger>
      <PopoverContent
        className="w-[420px] p-0 rounded-lg shadow-lg border"
        align="start"
        sideOffset={8}
      >
        <div className="p-4 space-y-3">
          {/* 标题与站点信息 */}
          <div className="flex items-start gap-3">
            {data.favicon && (
              <img
                src={data.favicon}
                alt=""
                className="w-4 h-4 mt-0.5 rounded-sm flex-shrink-0 border bg-muted object-contain"
                onError={(e) => {
                  // 如果图标加载失败，隐藏图标
                  (e.target as HTMLImageElement).style.display = "none";
                }}
              />
            )}
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-sm leading-tight line-clamp-2">
                {data.title}
              </h4>
              {data.url && (
                <p className="mt-1 text-xs text-muted-foreground truncate">
                  {(() => {
                    try {
                      const u = new URL(data.url);
                      return u.hostname.replace(/^www\./, "");
                    } catch {
                      return data.url;
                    }
                  })()}
                </p>
              )}
            </div>
          </div>

          {/* 引用内容 */}
          {data.context && (
            <div className="text-sm text-muted-foreground leading-relaxed">
              <p className="line-clamp-5">{data.context}</p>
            </div>
          )}

          {/* 链接 */}
          {data.url && (
            <div className="pt-2 border-t">
              <a
                href={data.url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-xs text-primary hover:text-primary/80 transition-colors"
              >
                <ExternalLink className="w-3 h-3" />
                查看原文
              </a>
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
