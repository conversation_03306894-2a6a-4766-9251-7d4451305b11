"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useConversation, ChatType } from "../context/ConversationContext";
import { SmartInput } from "@/components/chat/smart-input";
import { v4 as uuidv4 } from "uuid";
import { useTranslation } from "react-i18next";

export default function NewChatPage() {
  const [inputValue, setInputValue] = useState("");
  const { t } = useTranslation();
  const router = useRouter();
  const {
    sendMessage,
    createNewConversation,
    clearMessages,
    setActiveConversationId,
    isStreaming,
  } = useConversation();

  // 进入新会话页面时清空状态 - 只执行一次
  useEffect(() => {
    clearMessages();
    setActiveConversationId(null); // 清空活跃会话ID
  }, []); // 空依赖数组，只在组件挂载时执行一次

  const handleSendMessage = async (message: string) => {
    if (message.trim() === "") return;

    const tempConversationId = `temp_${uuidv4()}`;

    // 创建临时会话，不传入聊天类型选项，使用上下文中当前选中的类型
    await createNewConversation(tempConversationId);

    // 跳转到临时会话页面
    router.push(`/chat/${tempConversationId}`);

    // 延迟发送消息，确保路由跳转完成
    setTimeout(() => {
      // 发送消息，使用上下文中当前选中的类型
      sendMessage(message, tempConversationId);
      setInputValue(""); // 清空输入
    }, 100);
  };

  return (
    <div className="h-full flex flex-col items-center justify-center px-4">
      {/* 招呼语 */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center mb-6">
          <span className="text-4xl mr-3">🌟</span>
          <h1 className="text-4xl font-medium text-foreground">
            Hey there, 你好
          </h1>
        </div>
      </div>

      {/* 智能输入框 */}
      <SmartInput
        value={inputValue}
        onChange={setInputValue}
        onSend={handleSendMessage}
        placeholder={t("chat.typeMessage")}
        showOptions={true}
        disabled={isStreaming}
        minHeight={96}
      />
    </div>
  );
}
