import toast, { Toaster } from 'react-hot-toast';
import { useTranslation } from '@/i18n/useTranslation';

export const ToastManager = () => {
  return <Toaster position="top-right" toastOptions={{
    duration: 5000,
    style: {
      background: '#363636',
      color: '#fff',
    },
  }} />;
};

export const showToast = {
  success: (message: string) => toast.success(message),
  error: (message: string) => toast.error(message),
  warning: (message: string) => toast(message, { icon: '⚠️' }),
  info: (message: string) => toast(message),
};

// 国际化版本的toast - 修改为接受翻译函数作为参数
export const showToastT = {
  success: (key: string, t: (key: string, options?: Record<string, any>) => string, options?: Record<string, any>) => {
    return toast.success(t(key, options));
  },
  error: (key: string, t: (key: string, options?: Record<string, any>) => string, options?: Record<string, any>) => {
    return toast.error(t(key, options));
  },
  warning: (key: string, t: (key: string, options?: Record<string, any>) => string, options?: Record<string, any>) => {
    return toast(t(key, options), { icon: '⚠️' });
  },
  info: (key: string, t: (key: string, options?: Record<string, any>) => string, options?: Record<string, any>) => {
    return toast(t(key, options));
  },
};

// 便捷函数，用于React组件内部
export const useToastT = () => {
  const { t } = useTranslation();
  
  return {
    success: (key: string, options?: Record<string, any>) => showToastT.success(key, t, options),
    error: (key: string, options?: Record<string, any>) => showToastT.error(key, t, options),
    warning: (key: string, options?: Record<string, any>) => showToastT.warning(key, t, options),
    info: (key: string, options?: Record<string, any>) => showToastT.info(key, t, options),
  };
};