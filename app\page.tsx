"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // 检查用户是否已登录
    const accessToken = document.cookie
      .split('; ')
      .find(row => row.startsWith('access_token='))
      ?.split('=')[1];

    if (accessToken) {
      // 用户已登录，重定向到聊天页面
      router.push('/chat');
    } else {
      // 用户未登录，重定向到登录页面
      router.push('/login');
    }
  }, [router]);

  return (
    <></>
  );
}
