import React from 'react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { cn } from '@/lib/utils';

interface SqlDisplayProps {
  sql: string;
  className?: string;
}

export function SqlDisplay({ sql, className = '' }: SqlDisplayProps) {
  if (!sql) {
    return <div className="p-4 text-muted-foreground">无 SQL 查询</div>;
  }

  return (
    <div className={cn('p-4', className)}>
      <div className="relative rounded-md bg-muted overflow-hidden">
        <SyntaxHighlighter
          language="sql"
          style={oneDark}
          customStyle={{
            margin: 0,
            padding: '1rem',
            fontSize: '0.875rem',
            lineHeight: '1.5',
            background: 'hsl(var(--muted))',
          }}
          codeTagProps={{
            className: 'font-mono',
          }}
          showLineNumbers={false}
          wrapLines={true}
        >
          {sql}
        </SyntaxHighlighter>
      </div>
    </div>
  );
}
