'use client';

import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, FileText, FileCode, Volume2, ExternalLink, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { KnowledgePanelData } from '@/app/chat/types/right-panel';

interface KnowledgeViewerProps {
  data: KnowledgePanelData | undefined;
}

function getDisplayName(item: any) {
  if (item.file_name) return item.file_name;
  if (item.file_path) {
    try {
      const url = new URL(item.file_path);
      return decodeURIComponent(url.pathname.split("/").pop() || item.file_path);
    } catch {
      const segs = item.file_path.split("/");
      return decodeURIComponent(segs[segs.length - 1] || item.file_path);
    }
  }
  if (item.content_text) return item.content_text.slice(0, 16) + (item.content_text.length > 16 ? "…" : "");
  return "未知来源";
}

export function KnowledgeViewer({ data }: KnowledgeViewerProps) {
  const items = data?.items || [];
  const [currentIndex, setCurrentIndex] = useState(0);
  const total = items.length;
  const currentItem = items[currentIndex];

  const prev = () => setCurrentIndex((i) => (i - 1 + total) % total);
  const next = () => setCurrentIndex((i) => (i + 1) % total);

  React.useEffect(() => {
    setCurrentIndex(0);
  }, [items]);

  if (!total || !currentItem) {
    return (
      <div className="p-4">
        <p className="text-sm text-muted-foreground">暂无知识库内容</p>
      </div>
    );
  }

  const handleViewSource = () => {
    if (currentItem.file_path) {
      window.open(currentItem.file_path, '_blank');
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* 切片选择器 */}
      {total > 1 && (
        <div className="p-4 border-b border-border/50">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">选择切片:</span>
            <Select value={currentIndex.toString()} onValueChange={(value) => setCurrentIndex(parseInt(value))}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {items.map((item, index) => (
                  <SelectItem key={index} value={index.toString()}>
                    {getDisplayName(item)} ({index + 1}/{total})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* 内容区域 */}
      <div className="flex-1 p-4">
        <div className="space-y-4">
          {/* 文件信息 */}
          <div className="p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center gap-3 mb-3">
              <FileText className="w-5 h-5 text-primary" />
              <span className="font-medium text-base">{getDisplayName(currentItem)}</span>
            </div>
            
            {/* 查看原文按钮 */}
            {currentItem.file_path && (
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full"
                onClick={handleViewSource}
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                查看原文
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* 底部导航 */}
      {total > 1 && (
        <div className="p-4 border-t border-border/50 bg-muted/30">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={prev}
              disabled={total <= 1}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            
            <span className="text-sm text-muted-foreground font-mono">
              {currentIndex + 1} / {total}
            </span>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={next}
              disabled={total <= 1}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
