"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useConversation } from './context/ConversationContext';

export default function ChatPage() {
  const router = useRouter();
  const { conversations } = useConversation();

  useEffect(() => {
    const accessToken = document.cookie
      .split('; ')
      .find(row => row.startsWith('access_token='))
      ?.split('=')[1];
      
    if (!accessToken) {
      router.push('/login');
      return;
    }

    // 如果有会话，跳转到第一个会话，否则跳转到新建会话页面
    if (conversations.length > 0) {
      router.push(`/chat/${conversations[0].id}`);
    } else {
      router.push('/chat/new');
    }
  }, [router, conversations]);

  return (
    <div className="flex h-full w-full items-center justify-center">
      <div className="text-muted-foreground">加载中...</div>
    </div>
  );
}