'use client';

import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Pencil } from 'lucide-react';
import { motion } from 'framer-motion';
import * as Dialog from '@/components/ui/dialog';
import { Conversation } from './sidebar';

interface EditConversationModalProps {
  conversation: Conversation;
  onUpdate: (id: string, title: string) => Promise<void>;
}

export function EditConversationModal({ conversation, onUpdate }: EditConversationModalProps) {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [title, setTitle] = useState(conversation.title);
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    if (!title.trim()) return;
    
    setIsLoading(true);
    try {
      await onUpdate(conversation.id, title.trim());
      setIsOpen(false);
    } catch (error) {
      console.error('更新会话标题失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (open) {
      setTitle(conversation.title);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave();
    }
  };

  return (
    <Dialog.Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <Dialog.DialogTrigger asChild>
        <motion.button
          className="p-1.5 rounded-md hover:bg-accent/50 text-foreground/70 hover:text-foreground transition-colors"
          aria-label={t('chat.editConversation')}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <Pencil className="h-3.5 w-3.5" />
        </motion.button>
      </Dialog.DialogTrigger>
      
      <Dialog.DialogContent className="sm:max-w-md">
        <Dialog.DialogHeader>
          <Dialog.DialogTitle className="flex items-center gap-2">
            <Pencil className="h-5 w-5" />
            {t('chat.editConversation')}
          </Dialog.DialogTitle>
          <Dialog.DialogDescription>
            {t('chat.editConversationDescription', '修改会话标题')}
          </Dialog.DialogDescription>
        </Dialog.DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label htmlFor="conversation-title" className="text-sm font-medium">
              {t('chat.conversationTitle', '会话标题')}
            </label>
            <input
              id="conversation-title"
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              onKeyDown={handleKeyPress}
              className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors"
              placeholder={t('chat.enterConversationTitle', '请输入会话标题')}
              autoFocus
            />
          </div>
        </div>

        <Dialog.DialogFooter>
          <Dialog.DialogClose asChild>
            <motion.button
              className="px-4 py-2 text-sm font-medium text-muted-foreground hover:text-foreground bg-background border border-border rounded-lg hover:bg-accent/50 transition-colors"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {t('common.cancel')}
            </motion.button>
          </Dialog.DialogClose>
          <motion.button
            onClick={handleSave}
            disabled={!title.trim() || isLoading}
            className="px-4 py-2 text-sm font-medium bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {isLoading ? t('common.saving', '保存中...') : t('common.save')}
          </motion.button>
        </Dialog.DialogFooter>
      </Dialog.DialogContent>
    </Dialog.Dialog>
  );
}