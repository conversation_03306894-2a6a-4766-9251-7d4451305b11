import { createParser } from "eventsource-parser";
import axios from "axios";

interface SSEOptions {
  onMessage: (content: string) => void;
  onError?: (error: Error) => void;
  onFinish?: (finalText: string) => void;
  headers?: Record<string, string>;
}

async function refreshTokenAndRetry(originalHeaders: Record<string, string>) {
  try {
    // 从cookie中获取刷新令牌
    const refreshToken = document.cookie
      .split("; ")
      .find((row) => row.startsWith("refresh_token="))
      ?.split("=")[1];

    if (!refreshToken) throw new Error("No refresh token");
    const {
      data: { data },
    } = await axios.get(`/api/auth/refresh-token/${refreshToken}`);
    const { access_token, refresh_token } = data;

    // 将新令牌存储到cookie中
    document.cookie = `access_token=${access_token}; path=/; max-age=86400`;
    document.cookie = `refresh_token=${refresh_token}; path=/; max-age=604800`;

    return {
      ...originalHeaders,
      Authorization: `Bearer ${access_token}`,
    };
  } catch (error) {
    // 清除cookie中的令牌
    document.cookie =
      "access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
    document.cookie =
      "refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
    window.location.href = "/login";
    throw error;
  }
}

export const streamSSE = async (
  endpoint: string,
  payload: Record<string, any>,
  options: SSEOptions
): Promise<() => void> => {
  const controller = new AbortController();
  let answerBuffer: any = {};
  let retryCount = 0;
  // 记录上次已经回调给 onMessage 的快照，避免重复发送无变化的数据
  let lastEmittedSnapshot: any = null;

  const makeRequest = async (headers: Record<string, string>) => {
    try {
      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...headers,
          ...options.headers,
        },
        body: JSON.stringify(payload),
        signal: controller.signal,
      });

      if (
        (response.status === 403 || response.status === 401) &&
        retryCount === 0
      ) {
        retryCount++;
        const newHeaders = await refreshTokenAndRetry(headers);
        return makeRequest(newHeaders);
      }

      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const reader = response.body?.getReader();
      if (!reader) throw new Error("Failed to get stream reader");

      const decoder = new TextDecoder();
      const parser = createParser({
        onEvent(event) {
          try {
            const data = JSON.parse(event.data);
            if (!answerBuffer.conversation_id) {
              answerBuffer = { ...data };
            } else {
              // 更新各个字段，保持累积效果
              if (data.answer !== undefined) {
                answerBuffer.answer = data.answer;
              }
              if (data.retrieval_sql !== undefined) {
                if (
                  JSON.stringify(answerBuffer.retrieval_sql) !==
                  JSON.stringify(data.retrieval_sql)
                ) {
                  answerBuffer.retrieval_sql = data.retrieval_sql;
                }
              }
              if (data.chart_data !== undefined) {
                if (
                  JSON.stringify(answerBuffer.chart_data) !==
                  JSON.stringify(data.chart_data)
                ) {
                  answerBuffer.chart_data = data.chart_data;
                }
              }
              if (data.retrieval_result !== undefined) {
                if (
                  JSON.stringify(answerBuffer.retrieval_result) !==
                  JSON.stringify(data.retrieval_result)
                ) {
                  answerBuffer.retrieval_result = data.retrieval_result;
                }
              }
              if (data.metadata !== undefined) {
                answerBuffer.metadata = data.metadata;
              }
              if (data.thought_data !== undefined) {
                answerBuffer.thought_data = data.thought_data;
              }
              // if (data.final_report !== undefined) {
              //   answerBuffer.final_report = data.final_report
              // }
              if (data.type === "research_end" && data.status === "completed") {
                answerBuffer.get_final_report = true;
              }
            }
            // 只有当与上一次发送给 onMessage 的快照不同，才触发回调
            const shouldEmit = (() => {
              if (!lastEmittedSnapshot) return true;
              // 逐字段比对，避免频繁 JSON.stringify 全量深比较
              const keysToCheck = [
                "answer",
                "retrieval_sql",
                "chart_data",
                "retrieval_result",
                "metadata",
                "thought_data",
                "conversation_id",
                "message_id",
                "type",
                "status",
              ];
              for (const k of keysToCheck) {
                const prevV = (lastEmittedSnapshot as any)?.[k];
                const currV = (answerBuffer as any)?.[k];
                const prevS = typeof prevV === "string" ? prevV : JSON.stringify(prevV);
                const currS = typeof currV === "string" ? currV : JSON.stringify(currV);
                if (prevS !== currS) return true;
              }
              return false;
            })();

            if (shouldEmit) {
              lastEmittedSnapshot = { ...answerBuffer };
              options.onMessage(answerBuffer);
            }
          } catch (e) {
            console.error("SSE data parse failed:", e);
            options.onError?.(
              e instanceof Error ? e : new Error("SSE data parse failed")
            );
          }
        },
      });

      const pump = async () => {
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            try {
              options.onFinish?.(answerBuffer);
            } finally {
              setTimeout(() => controller.abort(), 100);
            }
            break;
          }
          parser.feed(decoder.decode(value));
        }
      };

      pump().catch((e) => options.onError?.(e));
    } catch (error) {
      options.onError?.(
        error instanceof Error ? error : new Error("Stream failed")
      );
    }
  };

  const initialHeaders = {
    Authorization: `Bearer ${
      document.cookie
        .split("; ")
        .find((row) => row.startsWith("access_token="))
        ?.split("=")[1]
    }`,
  };

  await makeRequest(initialHeaders);
  return () => controller.abort();
};
