import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// 定义需要认证的路径
const protectedPaths = ['/chat'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // 检查是否访问受保护的路径
  const isProtectedPath = protectedPaths.some(path => pathname.startsWith(path));
  
  if (isProtectedPath) {
    // 获取访问令牌
    const accessToken = request.cookies.get('access_token')?.value;
    
    // 如果没有访问令牌，重定向到登录页面
    if (!accessToken) {
      return NextResponse.redirect(new URL('/login', request.url));
    }
  }
  
  // 对于其他路径，继续处理请求
  return NextResponse.next();
}

// 配置中间件匹配的路径
export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};