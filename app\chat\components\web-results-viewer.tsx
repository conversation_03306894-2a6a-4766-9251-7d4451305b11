"use client";

import React from "react";
import { ChevronRight, ExternalLink } from "lucide-react";
import { cn } from "@/lib/utils";
import type { WebPanelData } from "@/app/chat/types/right-panel";

function getDomain(url?: string) {
  if (!url) return "";
  try {
    const u = new URL(url);
    return u.hostname.replace(/^www\./, "");
  } catch {
    return "";
  }
}

interface WebResultsViewerProps {
  data?: WebPanelData;
  className?: string;
}

export const WebResultsViewer: React.FC<WebResultsViewerProps> = ({ data, className }) => {
  const items = data?.items ?? [];
  if (!items.length) {
    return (
      <div className={cn("p-6 text-sm text-muted-foreground", className)}>暂无网页搜索结果</div>
    );
  }

  return (
    <div className={cn("h-full overflow-y-auto", className)}>
      <div className="divide-y">
        {items.map((it, idx) => (
          <a
            key={idx}
            href={it.url}
            target="_blank"
            rel="noopener noreferrer"
            className="block p-4 hover:bg-muted/40 transition-colors"
          >
            <div className="flex items-start gap-3">
              {it.favicon ? (
                <img
                  src={it.favicon}
                  alt=""
                  className="h-5 w-5 mt-0.5 rounded-sm border bg-muted object-contain"
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = "none";
                  }}
                />
              ) : (
                <div className="h-5 w-5 mt-0.5 rounded-sm bg-muted" />
              )}
              <div className="min-w-0 flex-1">
                <div className="line-clamp-2 font-medium leading-snug">{it.title || it.url}</div>
                <div className="mt-0.5 text-xs text-muted-foreground truncate">{getDomain(it.url)}</div>
                {it.context && (
                  <p className="mt-2 text-sm text-muted-foreground line-clamp-3">{it.context}</p>
                )}
              </div>
              <ExternalLink className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
            </div>
          </a>
        ))}
      </div>
    </div>
  );
};

export default WebResultsViewer;
