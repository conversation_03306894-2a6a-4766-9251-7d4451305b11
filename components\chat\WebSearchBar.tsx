"use client";

import React from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import type { CitationData } from "@/components/ui/citation";

interface WebSearchBarProps {
  items: CitationData[];
  className?: string;
  onClick?: () => void; // 若提供，则点击触发回调而非弹出 Popover
}

function getDomain(url?: string) {
  if (!url) return "";
  try {
    const u = new URL(url);
    return u.hostname.replace(/^www\./, "");
  } catch {
    return "";
  }
}

export const WebSearchBar: React.FC<WebSearchBarProps> = ({ items = [], className, onClick }) => {
  const list = Array.isArray(items) ? items : [];
  const total = list.length;
  if (!total) return null;

  // 展示前最多的图标数量
  const preview = list.slice(0, 4);

  const Capsule = (
    <button
      type="button"
      className={cn(
        "inline-flex items-center gap-2 rounded-full border bg-background/80 px-3 py-1.5 text-sm shadow-sm hover:bg-background transition-colors",
        className
      )}
      aria-label={`查看 ${total} 篇资料`}
      onClick={onClick}
    >
      <div className="flex -space-x-1">
        {preview.map((it, idx) => (
          <img
            key={idx}
            src={it.favicon || "/favicon.ico"}
            alt=""
            className="h-5 w-5 rounded-full border bg-muted object-contain"
            onError={(e) => {
              (e.target as HTMLImageElement).style.display = "none";
            }}
          />
        ))}
      </div>
      <span className="text-xs text-foreground/80">{total} 篇资料</span>
      <ChevronRight className="h-4 w-4 text-muted-foreground" />
    </button>
  );

  // 如果未提供 onClick，保持原有 Popover 展开行为；否则仅作为触发按钮
  if (!onClick) {
    return (
      <Popover>
        <PopoverTrigger asChild>{Capsule}</PopoverTrigger>
        <PopoverContent className="w-96 p-0" sideOffset={8}>
          <div className="max-h-80 overflow-y-auto">
            {list.map((it, idx) => (
              <a
                key={idx}
                href={it.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-start gap-3 p-3 hover:bg-muted/50 transition-colors"
              >
                {it.favicon ? (
                  <img
                    src={it.favicon}
                    alt=""
                    className="h-4 w-4 mt-0.5 rounded-sm border bg-muted object-contain"
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = "none";
                    }}
                  />
                ) : (
                  <div className="h-4 w-4 mt-0.5 rounded-sm bg-muted" />
                )}
                <div className="min-w-0 flex-1">
                  <div className="line-clamp-2 text-sm font-medium leading-snug">{it.title || it.url}</div>
                  <div className="mt-0.5 text-xs text-muted-foreground truncate">{getDomain(it.url)}</div>
                </div>
                <ChevronRight className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
              </a>
            ))}
          </div>
        </PopoverContent>
      </Popover>
    );
  }

  return Capsule;
};

export default WebSearchBar;
