'use client';

import { cn } from '@/lib/utils';

type LoadingMessageProps = {
  className?: string;
  /** Text to display next to the loading dots */
  text?: string;
};

export function LoadingMessage({ 
  className,
  text = '正在思考中' 
}: LoadingMessageProps) {
  return (
    <div className={cn('relative inline-flex items-baseline gap-1.5 text-sm text-muted-foreground', className)}>
      <span className="relative">
        {text}
        <span className="absolute -right-4.5 bottom-1 inline-flex items-center gap-0.5">
          <span className="inline-block h-1 w-1 rounded-full bg-primary/70 animate-pulse"></span>
          <span 
            className="inline-block h-1 w-1 rounded-full bg-primary/70 animate-pulse" 
            style={{ animationDelay: '0.2s' }}
          ></span>
          <span 
            className="inline-block h-1 w-1 rounded-full bg-primary/70 animate-pulse" 
            style={{ animationDelay: '0.4s' }}
          ></span>
        </span>
      </span>
    </div>
  );
}
