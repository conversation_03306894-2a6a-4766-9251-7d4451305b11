'use client';

import { useTranslation } from 'react-i18next';
import { useI18n } from '@/i18n/I18nContext';
import { useTheme } from 'next-themes';
import { Settings, Languages, Sun, Moon, Monitor } from 'lucide-react';
import { motion } from 'framer-motion';
import * as Dialog from '@/components/ui/dialog';
import * as DropdownMenu from '@/components/ui/dropdown-menu';

export function SettingsModal() {
  const { t } = useTranslation();
  const { language, changeLanguage } = useI18n();
  const { setTheme, theme } = useTheme();

  return (
    <Dialog.Dialog>
      <Dialog.DialogTrigger asChild>
        <motion.button
          className="p-2 rounded-lg hover:bg-accent/50 text-muted-foreground hover:text-foreground transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          aria-label={t('common.settings')}
        >
          <Settings className="h-4 w-4" />
        </motion.button>
      </Dialog.DialogTrigger>

      <Dialog.DialogContent className="sm:max-w-md">
        <Dialog.DialogHeader>
          <Dialog.DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            {t('common.settings')}
          </Dialog.DialogTitle>
          <Dialog.DialogDescription>
            {t('settings.description')}
          </Dialog.DialogDescription>
        </Dialog.DialogHeader>

        <div className="space-y-6 py-4">
          {/* 语言设置 */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Languages className="h-4 w-4 text-muted-foreground" />
              <h3 className="font-medium text-sm">{t('settings.language')}</h3>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <motion.button
                className={`p-3 rounded-lg border transition-all ${language === 'zh'
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'bg-background border-border hover:bg-accent/50'
                  }`}
                onClick={() => changeLanguage('zh')}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="text-sm font-medium">中文</div>
                <div className="text-xs opacity-80">Chinese</div>
              </motion.button>
              <motion.button
                className={`p-3 rounded-lg border transition-all ${language === 'en'
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'bg-background border-border hover:bg-accent/50'
                  }`}
                onClick={() => changeLanguage('en')}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="text-sm font-medium">English</div>
                <div className="text-xs opacity-80">英语</div>
              </motion.button>
            </div>
          </div>

          {/* 主题设置 */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Sun className="h-4 w-4 text-muted-foreground" />
              <h3 className="font-medium text-sm">{t('settings.theme')}</h3>
            </div>
            <div className="grid grid-cols-3 gap-2">
              <motion.button
                className={`p-3 rounded-lg border transition-all flex flex-col items-center gap-1 ${theme === 'light'
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'bg-background border-border hover:bg-accent/50'
                  }`}
                onClick={() => setTheme('light')}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Sun className="h-4 w-4" />
                <span className="text-xs font-medium">{t('theme.light')}</span>
              </motion.button>
              <motion.button
                className={`p-3 rounded-lg border transition-all flex flex-col items-center gap-1 ${theme === 'dark'
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'bg-background border-border hover:bg-accent/50'
                  }`}
                onClick={() => setTheme('dark')}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Moon className="h-4 w-4" />
                <span className="text-xs font-medium">{t('theme.dark')}</span>
              </motion.button>
              <motion.button
                className={`p-3 rounded-lg border transition-all flex flex-col items-center gap-1 ${theme === 'system'
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'bg-background border-border hover:bg-accent/50'
                  }`}
                onClick={() => setTheme('system')}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Monitor className="h-4 w-4" />
                <span className="text-xs font-medium">{t('theme.system')}</span>
              </motion.button>
            </div>
          </div>
        </div>
      </Dialog.DialogContent>
    </Dialog.Dialog>
  );
}
