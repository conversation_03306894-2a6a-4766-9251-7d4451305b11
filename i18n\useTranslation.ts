"use client";

import { useCallback, useContext } from 'react';
import i18n, { type TFunction } from 'i18next';
import { I18nContext } from '@/i18n/I18nContext';

export function useTranslation() {
  const context = useContext(I18nContext);
  
  // 添加类型检查，确保context存在
  if (!context) {
    throw new Error('useTranslation must be used within I18nProvider');
  }
  
  const { language } = context;
  
  const t = useCallback((key: string, options?: Record<string, any>) => {
    return i18n.t(key, { lng: language, ...options });
  }, [language]) as TFunction;

  return { t };
}