// types/request.d.ts 或 utils/request.ts
import { showToast } from '@/components/toast';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// 定义通用的 API 响应结构（根据你的后端）
export interface ApiResponse<T = any> {
  code?: number;
  message?: string;
  data: T;
  total?: number;
  page?: number;
  pageSize?: number;
  [key: string]: any;
}

// Request 类的返回类型：由于你在响应拦截器中 return response.data，
// 所以 get/post 等方法返回的就是 T（而不是 AxiosResponse<T>）
class Request {
  private instance: AxiosInstance;
  private baseConfig = {
    baseURL: `${process.env.NEXT_PUBLIC_API_URL}/api`,
    timeout: 60000,
  };

  constructor(config?: AxiosRequestConfig) {
    this.instance = axios.create({
      ...this.baseConfig,
      ...config,
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const token = document.cookie
          .split('; ')
          .find((row) => row.startsWith('access_token='))
          ?.split('=')[1];

        if (token) {
          config.headers['Authorization'] = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器：关键！返回的是 response.data
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response.data; // 👈 重点：这里只返回 data
      },
      async (error) => {
        const originalRequest = error.config;
        const { response } = error;
        const no_auth_code = [10006, 10007, 10008, 10009];
        const code = response?.data?.code;

        if (no_auth_code.includes(code) && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = document.cookie
              .split('; ')
              .find((row) => row.startsWith('refresh_token='))
              ?.split('=')[1];

            if (refreshToken) {
              const refreshResponse = await axios.get<{ data: { access_token: string; refresh_token: string } }>(
                `/api/auth/refresh-token/${refreshToken}`
              );

              const { access_token, refresh_token } = refreshResponse.data.data;

              document.cookie = `access_token=${access_token}; path=/; max-age=86400`;
              document.cookie = `refresh_token=${refresh_token}; path=/; max-age=604800`;

              originalRequest.headers['Authorization'] = `Bearer ${access_token}`;
              return this.instance(originalRequest);
            }
          } catch (refreshError) {
            document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
            document.cookie = 'refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
            showToast.error('Session expired. Please login again.');
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }

        if (response) {
          const { data } = response;
          switch (response.status) {
            case 401:
              document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
              document.cookie = 'refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
              window.location.href = '/login';
              showToast.error(data.message ? data.message : 'Unauthorized access');
              break;
            case 403:
              showToast.error(data.message ? data.message : 'No permission');
              break;
            case 404:
              showToast.error(data.message ? data.message : 'Resource not found');
              break;
            default:
              showToast.error(data.message ? data.message : 'Server error');
          }
        } else {
          showToast.error('Network error');
        }
        return Promise.reject(error);
      }
    );
  }

  // ✅ 修改返回类型为 Promise<T>，因为我们拦截了 response.data
  public async get<T>(url: string, params?: any): Promise<T> {
    return this.instance.get(url, { params });
  }

  public async post<T>(url: string, data?: any): Promise<T> {
    return this.instance.post(url, data);
  }

  public async put<T>(url: string, data?: any): Promise<T> {
    return this.instance.put(url, data);
  }

  public async delete<T>(url: string): Promise<T> {
    return this.instance.delete(url);
  }
}

export const request = new Request();