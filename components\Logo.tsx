import * as React from "react";

export interface LogoIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

export const LogoIcon: React.FC<LogoIconProps> = ({ className, color, ...props }) => {
  return (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      aria-hidden="true"
      focusable="false"
      {...props}
    >
      <path
        d="M341.295002 625.822004a113.764999 113.764999 0 0 1 113.765999 113.764999v170.647998A113.764999 113.764999 0 0 1 341.295002 1024h-227.529998A113.764999 113.764999 0 0 1 0.000005 910.235001V739.644003a113.764999 113.764999 0 0 1 113.764999-113.764999h227.529998z m568.825994-227.529998a113.764999 113.764999 0 0 1 113.764999 113.764999v398.177996A113.764999 113.764999 0 0 1 910.120996 1024H625.707999a113.764999 113.764999 0 0 1-113.764999-113.764999V512.170005c0-62.855999 50.967-113.764999 113.764999-113.764999l284.412997-0.057zM341.295002 0.00001a113.764999 113.764999 0 0 1 113.480999 105.232999l0.285 8.532v341.295997a113.764999 113.764999 0 0 1-113.765999 113.764998h-227.529998A113.764999 113.764999 0 0 1 0.000005 455.060006V113.765009C0.000005 50.96701 50.967005 0.00001 113.765004 0.00001h227.529998z m568.825994 0a113.764999 113.764999 0 0 1 113.479999 105.232999l0.285 8.532V227.530008a113.764999 113.764999 0 0 1-113.764999 113.764999H625.707999A113.764999 113.764999 0 0 1 511.943 227.530008V113.765009A113.764999 113.764999 0 0 1 625.707999 0.00001h284.412997z"
        fill={color ?? "#817EE8"}
      />
    </svg>
  );
};
