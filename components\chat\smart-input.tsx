'use client';

import { useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { useConversation, ChatOption } from '@/app/chat/context/ConversationContext';

interface SmartInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: (message: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  showOptions?: boolean;
  maxRows?: number;
  minHeight?: number; // px, 控制默认高度
  // 是否处于流式输出中；为 true 时用“停止”替换发送按钮
  isStreaming?: boolean;
  // 点击“停止”事件
  onStop?: () => void;
}

export function SmartInput({
  value,
  onChange,
  onSend,
  placeholder = "How can I help you today?",
  disabled = false,
  className,
  showOptions = true,
  maxRows = 6,
  minHeight = 60,
  isStreaming = false,
  onStop
}: SmartInputProps) {
  const { 
    chatOptions,
    chatType,
    setChatType
  } = useConversation();
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 自动调整 textarea 高度
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const lineHeight = 24; // 大约的行高
      const maxHeight = lineHeight * maxRows;
      textarea.style.height = `${Math.min(scrollHeight, maxHeight)}px`;
    }
  }, [value, maxRows]);

  const handleSend = () => {
    if (value.trim() === '' || disabled) return;
    onSend(value.trim());
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleOptionSelect = (option: ChatOption) => {
    // 再次点击同一项则回到默认 data，否则切换到该类型
    const nextType = chatType === option.id ? "data" : option.id;
    setChatType(nextType);
  };

  return (
    <div className={cn("w-full max-w-2xl", className)}>
      <div className={cn(
        "bg-background border border-border/50 rounded-2xl shadow-lg backdrop-blur-sm transition-all duration-200",
        "p-3"
      )}>
        {/* 输入区域 */}
        <div className="relative rounded-xl border border-border/30">
          <textarea
            ref={textareaRef}
            rows={1}
            className={cn(
              "w-full px-4 py-4 pr-16 rounded-t-xl bg-transparent text-foreground",
              "placeholder:text-muted-foreground/60 focus:outline-none resize-none",
              "transition-all duration-200",
              disabled && "opacity-50 cursor-not-allowed",
              showOptions && chatOptions.length > 0 ? "rounded-t-xl" : "rounded-xl"
            )}
            placeholder={placeholder}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyPress}
            disabled={disabled}
            style={{ minHeight }}
          />

          {/* 底部选项和发送按钮在同一行 */}
          {showOptions && chatOptions.length > 0 && (
            <div className="flex items-center justify-between px-4 py-2 border-t border-border/30 rounded-b-xl bg-muted/20">
              {/* 选项区域 */}
              <div className="flex flex-wrap gap-2 flex-1">
                {chatOptions.map((option) => (
                  <button
                    key={option.id}
                    onClick={() => handleOptionSelect(option)}
                    className={cn(
                      "inline-flex items-center gap-1.5 px-2.5 py-1 rounded-md text-xs font-medium transition-all duration-200",
                      "border border-border/50 hover:border-primary/50 hover:bg-primary/5",
                      chatType === option.id
                        ? "bg-primary/10 border-primary text-primary"
                        : "bg-background/50 text-muted-foreground hover:text-foreground"
                    )}
                    disabled={disabled}
                  >
                    <span className="text-sm">{option.icon}</span>
                    <span>{option.label}</span>
                  </button>
                ))}
              </div>

              {/* 发送/停止按钮 */}
              <div className="ml-3 flex-shrink-0">
                {isStreaming ? (
                  <button
                    className={cn(
                      "p-2 rounded-xl transition-all shadow-md",
                      "inline-flex items-center justify-center",
                      "bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-lg"
                    )}
                    onClick={onStop}
                    aria-label="停止生成"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                    >
                      <rect x="6" y="6" width="12" height="12" rx="2" />
                    </svg>
                  </button>
                ) : (
                  <button
                    className={cn(
                      "p-2 rounded-xl transition-all shadow-md",
                      "inline-flex items-center justify-center",
                      "disabled:opacity-50 disabled:cursor-not-allowed",
                      value.trim()
                        ? "bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-lg"
                        : "bg-muted text-muted-foreground"
                    )}
                    onClick={handleSend}
                    disabled={!value.trim() || disabled}
                    aria-label="发送"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className={cn(
                        "transition-transform duration-200",
                        value.trim() && "hover:translate-x-0.5"
                      )}
                    >
                      <path d="m22 2-7 20-4-9-9-4Z" />
                      <path d="M22 2 11 13" />
                    </svg>
                  </button>
                )}
              </div>
            </div>
          )}

          {/* 如果没有选项，发送按钮保持在原位置 */}
          {(!showOptions || chatOptions.length === 0) && (
            <div className="absolute right-3 bottom-3">
              {isStreaming ? (
                <button
                  className={cn(
                    "p-2 rounded-xl transition-all shadow-md",
                    "inline-flex items-center justify-center",
                    "bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-lg"
                  )}
                  onClick={onStop}
                  aria-label="停止生成"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <rect x="6" y="6" width="12" height="12" rx="2" />
                  </svg>
                </button>
              ) : (
                <button
                  className={cn(
                    "p-2 rounded-xl transition-all shadow-md",
                    "inline-flex items-center justify-center",
                    "disabled:opacity-50 disabled:cursor-not-allowed",
                    value.trim()
                      ? "bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-lg"
                      : "bg-muted text-muted-foreground"
                  )}
                  onClick={handleSend}
                  disabled={!value.trim() || disabled}
                  aria-label="发送"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className={cn(
                      "transition-transform duration-200",
                      value.trim() && "hover:translate-x-0.5"
                    )}
                  >
                    <path d="m22 2-7 20-4-9-9-4Z" />
                    <path d="M22 2 11 13" />
                  </svg>
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}