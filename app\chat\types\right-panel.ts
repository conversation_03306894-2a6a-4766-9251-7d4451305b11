// 右侧面板类型定义
export type RightPanelType = 'knowledge' | 'web' | 'report' | 'code' | 'none';

// 知识库数据接口
export interface KnowledgePanelData {
  items: Array<{
    file_name?: string;
    file_path?: string;
    file_size?: number;
    word_count?: number;
    chunk_order?: number;
    file_format?: string;
    content_text?: string;
    created_time?: string;
    modified_time?: string;
    start_ms?: number;
    end_ms?: number;
  }>;
}

// 网页搜索结果数据接口（与 CitationData 结构兼容）
export interface WebPanelData {
  items: Array<{
    context?: string;
    favicon?: string;
    order?: number;
    title: string;
    url: string;
  }>;
}

// 报告数据接口
export interface ReportPanelData {
  title: string;
  content: string;
  generatedAt: string;
  type: 'summary' | 'analysis' | 'detailed';
}

// 代码执行数据接口
export interface CodePanelData {
  code: string;
  output: string;
  language: string;
  executedAt: string;
}

// 右侧面板数据联合类型
export type RightPanelData = 
  | { type: 'knowledge'; data: KnowledgePanelData }
  | { type: 'web'; data: WebPanelData }
  | { type: 'report'; data: ReportPanelData }
  | { type: 'code'; data: CodePanelData }
  | { type: 'none'; data: null };

// 右侧面板状态接口
export interface RightPanelState {
  visible: boolean;
  type: RightPanelType;
  data: RightPanelData | null;
}

// 右侧面板控制接口
export interface RightPanelControls {
  openPanel: (type: RightPanelType, data: any) => void;
  closePanel: () => void;
  togglePanel: () => void;
}
