import React, { useEffect, useState, useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import { Skeleton } from '@/components/ui/skeleton';

interface ChartDisplayProps {
  chartData: any;
  className?: string;
}

// 图表配置默认值
const defaultChartOption = {
  title: { text: '加载中...' },
  xAxis: { type: 'category', data: [] },
  yAxis: { type: 'value' },
  series: [{ data: [], type: 'line' }]
};

export function ChartDisplay({ chartData, className = '' }: ChartDisplayProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [prevChartData, setPrevChartData] = useState<any>(null);
  const [displayData, setDisplayData] = useState<any>(defaultChartOption);

  // 处理图表数据更新
  useEffect(() => {
    if (!chartData) {
      setIsLoading(true);
      return;
    }

    // 如果数据有变化才更新
    if (JSON.stringify(chartData) !== JSON.stringify(prevChartData)) {
      setPrevChartData(chartData);
      
      // 确保数据格式正确
      if (chartData.series && chartData.series.length > 0) {
        setDisplayData(chartData);
      } else {
        // 如果数据不完整，使用默认配置
        setDisplayData({
          ...defaultChartOption,
          title: { text: '图表数据加载中...' }
        });
      }
      
      setIsLoading(false);
    }
  }, [chartData, prevChartData]);

  // 使用useMemo优化图表渲染
  const chartComponent = useMemo(() => {
    if (isLoading) {
      return (
        <div className="w-full h-[400px] flex items-center justify-center">
          <Skeleton className="h-full w-full" />
        </div>
      );
    }

    return (
      <div className="w-full h-[400px] p-4">
        <ReactECharts
          option={displayData}
          style={{ height: '100%', width: '100%' }}
          opts={{ renderer: 'svg' }}
          notMerge={false} // 使用merge模式，只更新变化的部分
          lazyUpdate={true}
          theme="light"
          showLoading={isLoading}
          loadingOption={{
            text: '加载中...',
            color: '#1890ff',
            textColor: '#000',
            maskColor: 'rgba(255, 255, 255, 0.8)',
          }}
        />
      </div>
    );
  }, [displayData, isLoading]);

  return <div className={className}>{chartComponent}</div>;
}
