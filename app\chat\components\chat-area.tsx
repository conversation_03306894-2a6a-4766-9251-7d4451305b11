"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useConversation } from "../context/ConversationContext";
import { MessageBubble } from "./MessageBubble";
import { LoadingMessage } from "./loading-message";
import { SmartInput } from "@/components/chat/smart-input";
import type { RightPanelType } from "@/app/chat/types/right-panel";
import { useUI } from "@/app/chat/context/UIContext";

interface ChatAreaProps {}

export function ChatArea({}: ChatAreaProps) {
  const { t } = useTranslation();
  const [mounted, setMounted] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const { openRightPanel: openRightPanelGlobal } = useUI();

  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const {
    messages,
    loading,
    error,
    sendMessage,
    activeConversationId,
    isStreaming,
    streamingConversationId,
    stopGeneration,
  } = useConversation();

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [pushOffset, setPushOffset] = useState(0);
  const inputWrapperRef = useRef<HTMLDivElement>(null);
  // 可调的安全间距（越大位移越多）
  const PUSH_SAFE_GAP = 32;

  // 新方案：计算固定偏移量，确保历史消息被推走
  const pushNewMessage = useCallback(() => {
    const container = scrollContainerRef.current;
    const inputWrapper = inputWrapperRef.current;

    if (!container || !inputWrapper) return;

    // 已统一使用全局 Header，chat-area 不再占用头部高度
    const headerHeight = 0;
    const inputHeight = inputWrapper.clientHeight;

    // 固定的偏移值 = 容器高度 - 输入框高度 - 安全边距
    const offset =
      container.clientHeight - inputHeight - PUSH_SAFE_GAP - headerHeight;

    setPushOffset(offset);
  }, []);

  // 处理滚动事件 - 只在用户手动滚动时更新状态
  const handleScroll = useCallback(() => {
    if (!scrollContainerRef.current) return;

    const container = scrollContainerRef.current;
    const { scrollTop, scrollHeight, clientHeight } = container;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

    setIsUserScrolling(!isNearBottom);
    setShowScrollButton(!isNearBottom && messages.length > 0);
  }, [messages.length]);

  // 在 AI 输出完成后轻微延时复位位移
  useEffect(() => {
    if (!isStreaming) {
      const id = setTimeout(() => setPushOffset(0), 120);
      return () => clearTimeout(id);
    }
  }, [isStreaming]);

  // 窗口尺寸变化时，如存在位移则按新高度重算
  useEffect(() => {
    const onResize = () => {
      if (pushOffset > 0) {
        pushNewMessage();
      }
    };
    window.addEventListener("resize", onResize);
    return () => window.removeEventListener("resize", onResize);
  }, [pushOffset, pushNewMessage]);

  // 当消息变化且未流式，复位位移，保持自然位置
  useEffect(() => {
    if (!isStreaming) setPushOffset(0);
  }, [messages.length, isStreaming]);

  // 处理用户发送消息：先滚动到底部，然后应用位移
  const handleSendMessage = useCallback(
    (content: string) => {
      if (!content) return;

      sendMessage(content, activeConversationId!);

      // 使用setTimeout确保DOM更新完成
      setTimeout(() => {
        const container = scrollContainerRef.current;
        if (container) {
          // 1. 滚动到底部，让新消息可见
          container.scrollTo({
            top: container.scrollHeight,
            behavior: "smooth",
          });

          // 2. 在滚动动画结束后应用位移，将历史消息推走
          // 'smooth' 滚动大约需要 300-500ms, 我们给一个延迟
          setTimeout(() => {
            pushNewMessage();
          }, 300);
        }
      }, 100); // 等待新消息渲染
    },
    [pushNewMessage, sendMessage, activeConversationId]
  );

  // 初始化时不需要滚动，使用translateY位移来处理初始状态

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <div className="flex flex-col h-full bg-background">
      {/* 聊天消息区域 */}
      <div className="flex-1 relative overflow-hidden">
        <div
          ref={scrollContainerRef}
          className="absolute inset-0 overflow-y-auto scroll-smooth"
          onScroll={handleScroll}
        >
          <div className="max-w-3xl mx-auto w-full">
            {loading && (
              <div className="flex justify-center items-center h-full">
                <div className="animate-pulse">{t("chat.loadingMessages")}</div>
              </div>
            )}
            {error && (
              <div className="text-red-500 text-center p-4">{error}</div>
            )}

            {/* 消息容器 - 关键部分 */}
            <div
              ref={messagesContainerRef}
              className="space-y-6 px-4 py-6"
              style={{
                transform: `translateY(-${pushOffset}px)`,
                transition:
                  pushOffset > 0
                    ? "transform 300ms cubic-bezier(0.4, 0, 0.2, 1)"
                    : "none",
              }}
            >
              {/* 正常顺序：旧消息在上，新消息在下 */}
              {Array.isArray(messages) &&
                messages.map((message, index) => (
                  <div
                    key={
                      message.role === "assistant"
                        ? message.id
                        : `${message.id}-user`
                    }
                    className={`w-full transform transition-all duration-300 ease-out ${
                      index === messages.length - 1 ? "animate-slide-in" : ""
                    }`}
                  >
                    <MessageBubble
                      message={message}
                      isUser={message.role === "user"}
                      onOpenRightPanel={(type: RightPanelType, data: any) => {
                        // 统一使用全局的openRightPanel，layout会处理桌面端和移动端的不同显示方式
                        openRightPanelGlobal(type, data);
                      }}
                    />
                  </div>
                ))}

              {/* 显示加载中的消息 */}
              {isStreaming &&
                streamingConversationId === activeConversationId && (
                  <div className="w-full animate-slide-in">
                    <LoadingMessage />
                  </div>
                )}
            </div>
          </div>
        </div>

        {/* 回到最新消息按钮 */}
        {showScrollButton && (
          <button
            onClick={pushNewMessage}
            className="absolute bottom-4 right-4 bg-primary text-primary-foreground rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 z-10 hover:scale-110"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 14l-7 7m0 0l-7-7m7 7V3"
              />
            </svg>
          </button>
        )}
      </div>

      {/* 输入区域 - 复用 SmartInput 样式，与新会话一致 */}
      <div className="w-full max-w-3xl mx-auto px-4 pb-4" ref={inputWrapperRef}>
        <SmartInput
          value={inputValue}
          onChange={setInputValue}
          onSend={(msg) => {
            handleSendMessage(msg);
            setInputValue("");
          }}
          placeholder={t("chat.typeMessage")}
          disabled={loading || isStreaming}
          showOptions={true}
          className="w-full max-w-3xl"
          isStreaming={isStreaming}
          onStop={stopGeneration}
        />
      </div>

      {/* 添加动画样式 */}
      <style jsx>{`
        @keyframes slide-in {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-slide-in {
          animation: slide-in 0.4s ease-out;
        }
      `}</style>
    </div>
  );
}
