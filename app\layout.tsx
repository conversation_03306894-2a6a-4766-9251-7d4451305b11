import type { Viewport } from "next";
import "./globals.css";
import { ThemeProvider } from "./theme-provider";
import { I18nClientProvider } from "@/i18n/I18nClientProvider";
import { ToastManager } from "@/components/toast";
import { Quicksand, ZCOOL_KuaiLe } from "next/font/google";

// 配置字体 - 使用更特别的字体组合
const quicksand = Quicksand({ 
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-quicksand'
});

const zcoolKuaiLe = ZCOOL_KuaiLe({
  weight: ['400'],
  display: 'swap',
  variable: '--font-zcool',
  subsets: ['latin']
});

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#0c0c0c" },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning className={`${quicksand.variable} ${zcoolKuaiLe.variable}`}>
      <body suppressHydrationWarning className="font-sans">
        <I18nClientProvider>
          <ThemeProvider>
            {children}
            <ToastManager />
          </ThemeProvider>
        </I18nClientProvider>
      </body>
    </html>
  );
}
