"use client";

import { memo, useEffect, useRef, useState } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/cjs/styles/prism";
import { Citation, CitationData } from "@/components/ui/citation";
import { visitParents } from "unist-util-visit-parents";
import reactStringReplace from "react-string-replace";
import "../app/styles/markdown.scss";

// rehype插件：将所有文本包装成custom-typography
function rehypeWrapReference() {
  return function transformer(tree: any) {
    visitParents(tree, "text", (node: any, ancestors: any[]) => {
      const parent = ancestors[ancestors.length - 1];
      if (
        parent &&
        parent.tagName !== "custom-typography" &&
        parent.tagName !== "code"
      ) {
        node.type = "element";
        node.tagName = "custom-typography";
        node.properties = {};
        node.children = [{ type: "text", value: node.value }];
      }
    });
  };
}

// 文本渲染器：处理引用替换
const TextRenderer = ({
  children,
  metadata,
}: {
  children: any;
  metadata: any;
}) => {
  if (!children) return null;
  if (typeof children !== "string") return <>{children}</>;
  if (!metadata?.web_metadata || !metadata.web_metadata.length)
    return <>{children}</>;

  // 使用 reactStringReplace 替换引用
  const replacedContent = reactStringReplace(
    children,
    /\[(\d+)\]/g,
    (match, i) => {
      const index = parseInt(match, 10);
      const citationData = metadata.web_metadata.find(
        (item: any) => item.order === index
      );

      if (citationData) {
        return (
          <Citation
            key={`citation-${index}-${i}`}
            number={index}
            data={citationData}
            className="mx-0.5"
          />
        );
      }

      return `[${match}]`;
    }
  );

  return <>{replacedContent}</>;
};

interface MarkdownRendererProps {
  content: string;
  className?: string;
  animate?: boolean;
  isUser?: boolean;
  webMetadata?: CitationData[]; // 网页引用元数据
}

export const MarkdownRenderer = memo(
  ({
    content,
    className = "",
    animate = false,
    isUser = false,
    webMetadata = [],
  }: MarkdownRendererProps) => {
    // 仅在流式（animate=true）且内容长度增加时，触发一次轻微的淡入动画
    const prevLenRef = useRef(0);
    const firstRunRef = useRef(true);
    const [toggle, setToggle] = useState(false);

    useEffect(() => {
      if (firstRunRef.current) {
        // 首次渲染不做动画，直接记录当前长度
        prevLenRef.current = content.length;
        firstRunRef.current = false;
        return;
      }
      if (animate && content.length > prevLenRef.current) {
        setToggle((t) => !t); // 通过切换类名来重新触发 CSS 动画
      }
      prevLenRef.current = content.length;
    }, [animate, content]);

    const fadeClass = animate ? (toggle ? "md-fade-a" : "md-fade-b") : "";

    // 构建metadata对象
    const metadata = {
      web_metadata: webMetadata,
    };

    return (
      <div
        className={`markdown-body prose prose-sm dark:prose-invert max-w-none ${className} ${fadeClass} prose-p:leading-relaxed prose-p:my-3 prose-ul:my-2 prose-ol:my-2 prose-li:my-1 prose-headings:font-medium prose-h1:text-xl prose-h2:text-lg prose-h3:text-base ${
          isUser ? "text-white" : ""
        }`}
      >
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeWrapReference]}
          components={
            {
              // 使用自定义文本渲染器处理引用
              "custom-typography": ({
                children,
              }: {
                children: React.ReactNode;
              }) => <TextRenderer children={children} metadata={metadata} />,
              code: ({ node, className, children, ...props }: any) => {
                const match = /language-(\w+)/.exec(className || "");
                const isInline = !className?.includes("language-");

                if (isInline) {
                  return (
                    <code
                      className="px-1.5 py-0.5 rounded bg-muted/50 text-sm font-mono text-foreground/90"
                      style={{ fontSize: "0.9em" }}
                      {...props}
                    >
                      {children}
                    </code>
                  );
                }

                return (
                  <div className="relative my-4">
                    <SyntaxHighlighter
                      style={vscDarkPlus}
                      language={match?.[1] || "text"}
                      PreTag="div"
                      customStyle={{
                        background: "hsl(var(--muted))",
                        margin: 0,
                        padding: "1rem",
                        borderRadius: "0.5rem",
                        fontSize: "0.875rem",
                        lineHeight: "1.5",
                      }}
                      wrapLines={true}
                      showLineNumbers={false}
                    >
                      {String(children).replace(/\n$/, "")}
                    </SyntaxHighlighter>
                  </div>
                );
              },
            } as unknown as React.ComponentProps<
              typeof ReactMarkdown
            >["components"]
          }
        >
          {content}
        </ReactMarkdown>
      </div>
    );
  }
);

MarkdownRenderer.displayName = "MarkdownRenderer";
