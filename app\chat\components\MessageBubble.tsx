import React from "react";
import { motion } from "framer-motion";
import { Message, useConversation } from "../context/ConversationContext";
import type { RightPanelType } from "@/app/chat/types/right-panel";
import { MarkdownRenderer } from "@/components/markdown-renderer";
import { CitationData } from "@/components/ui/citation";
import dynamic from "next/dynamic";
import { InteractionButtons } from "./InteractionButtons";
import copy from "copy-to-clipboard";

// 动态导入 DataDisplay 组件，禁用 SSR
const DataDisplay = dynamic(() => import("@/components/DataDisplay"), {
  ssr: false,
  loading: () => (
    <div className="p-4 text-sm text-muted-foreground">加载数据中...</div>
  ),
});

// 动态导入合并后的KnowledgePanel组件，禁用SSR
const KnowledgePanel = dynamic(
  () => import("@/components/data-display/KnowledgePanel"),
  { ssr: false }
);

// 动态导入 WebSearchBar，禁用 SSR
const WebSearchBar = dynamic(() => import("@/components/chat/WebSearchBar"), {
  ssr: false,
});

interface MessageBubbleProps {
  message: Message;
  isUser: boolean;
  onOpenRightPanel?: (type: RightPanelType, data: any) => void;
}

export function MessageBubble({
  message,
  isUser,
  onOpenRightPanel,
}: MessageBubbleProps) {
  const { regenerateMessage, sendFeedback } = useConversation();
  const [copied, setCopied] = React.useState(false);
  // 使用useMemo来优化性能，避免重复计算
  const hasData = React.useMemo(() => {
    return (
      (message.chart_data && Object.keys(message.chart_data).length > 0) ||
      message.retrieval_sql ||
      (message.retrieval_result && message.retrieval_result.length > 0)
    );
  }, [message.chart_data, message.retrieval_sql, message.retrieval_result]);

  // 是否包含知识库元数据
  const knowledgeItems = (message as any)?.metadata?.chunk_metadata as
    | Array<Record<string, any>>
    | undefined;
  const hasKnowledge =
    !isUser && Array.isArray(knowledgeItems) && knowledgeItems.length > 0;

  // 获取网页引用数据
  const webMetadata = React.useMemo(() => {
    const metadata = (message as any)?.metadata?.web_metadata as
      | CitationData[]
      | undefined;
    return Array.isArray(metadata) ? metadata : [];
  }, [message]);

  return (
    <motion.div
      className="w-full"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className={`w-full ${isUser ? "flex justify-end" : ""}`}>
        <div className={isUser ? "max-w-3xl" : "w-full"}>
          <div
            className={
              isUser
                ? "bg-primary text-white rounded-2xl rounded-tr-none px-5 py-3 select-text break-words whitespace-pre-wrap max-w-full inline-block shadow-sm"
                : "w-full select-text space-y-4"
            }
          >
            {!isUser && hasData && (
              <div className="rounded-xl border bg-card p-4 shadow-sm">
                <DataDisplay
                  key={`${message.id}-data`} // 添加key强制重新渲染
                  chartData={message.chart_data}
                  sql={message.retrieval_sql}
                  tableData={message.retrieval_result}
                />
              </div>
            )}
            <MarkdownRenderer
              content={message.content}
              // 仅在助手的流式临时消息期间启用动画（id 为 temp_ 开头）
              animate={!isUser && message.id?.startsWith("temp_")}
              className={!isUser ? "leading-relaxed" : ""}
              isUser={isUser}
              webMetadata={webMetadata}
            />
            {/* 网络搜索来源条（图标 + 篇数）*/}
            {!isUser && webMetadata.length > 0 && (
              <div className="mt-3">
                <WebSearchBar
                  items={webMetadata}
                  onClick={() =>
                    onOpenRightPanel?.("web", { items: webMetadata })
                  }
                />
              </div>
            )}
            {hasKnowledge && (
              <div className="mt-3">
                <KnowledgePanel
                  items={knowledgeItems as any}
                  onViewSource={(items) => {
                    onOpenRightPanel?.("knowledge", { items });
                  }}
                />
              </div>
            )}
            {/* 功能按钮 */}
            {!isUser && (
              <div className="mt-3">
                <InteractionButtons
                  onRetry={() => regenerateMessage(message.id)}
                  onCopy={async () => {
                    try {
                      await copy(message.content || "");
                      setCopied(true);
                      setTimeout(() => setCopied(false), 1500);
                    } catch (e) {
                      console.error("复制失败:", e);
                    }
                  }}
                  onLike={() => sendFeedback(message.id, 1)}
                  onDislike={() => sendFeedback(message.id, 2)}
                  feedback={message.feedback ?? null}
                  copied={copied}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}
