{"name": "chat-workspace", "version": "0.1.0", "packageManager": "bun@1.2.21", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 3001", "lint": "biome check", "format": "biome format --write"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@types/lodash-es": "^4.17.12", "@types/mdast": "^4.0.4", "@types/prismjs": "^1.26.5", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "character-entities": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "echarts-for-react": "^3.0.2", "eventsource-parser": "^3.0.6", "framer-motion": "^12.23.12", "i18next": "^25.4.2", "i18next-browser-languagedetector": "^8.2.0", "idb-keyval": "^6.2.2", "lodash-es": "^4.17.21", "lucide-react": "^0.542.0", "next": "15.5.2", "next-i18next": "^15.4.2", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hot-toast": "^2.6.0", "react-markdown": "^10.1.0", "react-string-replace": "^1.1.1", "react-syntax-highlighter": "^15.6.6", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "sass": "^1.91.0", "tailwind-merge": "^3.3.1", "unist-util-visit": "^5.0.0", "unist-util-visit-parents": "^6.0.1", "uuid": "^11.1.0"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}