import { request } from '@/services/base'

enum API {
  GET_CONVERSATION_LIST = '/chat/conversations',
  CREATE_CONVERSATION = '/chat/conversations',
  GET_MESSAGES_BY_CONVERSION_ID = '/chat/conversation/${conversation_id}',
  SEND_MESSAGE = '/chat/conversations/${conversation_id}/messages',
  UPDATE_CONVERSATION = '/chat/conversation/${conversation_id}/name',
  DELETE_CONVERSATION = '/chat/conversation/${conversation_id}',
  FEEDBACK_MESSAGE = '/chat/message/${message_id}/feedbacks',
  SUGGESTION = '/chat/suggestion',
  DATASETLIST = '/chat/suggestion',
  SUGGESTION_BY_DATASET = '/chat/questions/by-dataset',
  STOP_CHAT_MESSAGE = '/chat/chat-message/{task_id}/stop',

  // 认证
  GET_APP_ID = '/auth/get-appid',
  AUTH_CODE = '/auth/auth',

  // 通用
  GET_CONFIG = '/config'
}

export const fetchConversationList = async (params: any) => {
  const data = await request.get(`${API.GET_CONVERSATION_LIST}`, { ...params })
  return data
}

export const fetchMessagesByConversionId = async (id: string) => {
  const data = await request.get(API.GET_MESSAGES_BY_CONVERSION_ID.replace('${conversation_id}', id))
  return data
}

export const updateConversation = async (id: string, name: string) => {
  const data = await request.post(API.UPDATE_CONVERSATION.replace('${conversation_id}', id), {
    name,
  })
  return data
}

export const deleteConversation = async (id: string) => {
  const data = await request.delete(API.DELETE_CONVERSATION.replace('${conversation_id}', id))
  return data
}

export const feedbackMessage = async (id: string, feedback: number) => {
  const data = await request.post(API.FEEDBACK_MESSAGE.replace('${message_id}', id), {
    feedback,
  })
  return data
}

export const fetchSuggestion = async () => {
  const data = await request.get(API.SUGGESTION)
  return data
}


export const fetchDataSetList = async () => {
  const data = await request.get(API.DATASETLIST)
  return data
}

export const fetchSuggestionByDataset = async (params: any) => {
  const data = await request.get(API.SUGGESTION_BY_DATASET, { ...params })
  return data
}

export const fetchGetConfig = async () => {
  const data = await request.get(API.GET_APP_ID)
  return data
}

interface AuthResponse {
  access_token: string;
  refresh_token: string;
  // 可以根据实际API响应添加更多字段
}

export const fetchAuthCode = async (code: string, source: string, username?: string, password?: string): Promise<{ data: AuthResponse }> => {
  const data = await request.get<{ data: AuthResponse }>(`${API.AUTH_CODE}`, { code, source, username, password })
  return data
}

export const fetchStopChatMessage = async (task_id: string) => {
  const data = await request.post(API.STOP_CHAT_MESSAGE.replace('{task_id}', task_id))
  return data
}

export const fetchGetCommonConfig = async () => {
  const data = await request.get(API.GET_CONFIG)
  return data
}
