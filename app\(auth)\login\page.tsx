"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '@/i18n';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ThemeToggle } from '@/components/theme-toggle';
import { fetchAuthCode } from '@/services/api';
import { useToastT } from '@/components/toast';

export default function LoginPage() {
  const router = useRouter();
  const { t } = useTranslation();
  const toastT = useToastT();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!username || !password) {
      toastT.error('auth.missingCredentials');
      return;
    }
    
    setLoading(true);
    try {
      const { data } = await fetchAuthCode('', 'web', username, password);
      const access_token = data.access_token;
      const refresh_token = data.refresh_token;
      
      // 存储令牌到cookie
      document.cookie = `access_token=${access_token}; path=/; max-age=86400`;
      document.cookie = `refresh_token=${refresh_token}; path=/; max-age=604800`;
      
      // 登录成功后跳转到主页
      router.push('/chat');
    } catch (error: any) {
      console.error('Login failed:', error);
      const errorMessage = error?.response?.data?.message || 'auth.loginFailed';
      toastT.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <Card className="w-full max-w-sm">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-xl mb-2">{t('auth.login')}</CardTitle>
            </div>
            <ThemeToggle />
          </div>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="username">{t('auth.username')}</Label>
              <Input
                id="username"
                type="text"
                placeholder={t('auth.enterUsername')}
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password">{t('auth.password')}</Label>
              <Input
                id="password"
                type="password"
                placeholder={t('auth.enterPassword')}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="remember"
                checked={rememberMe}
                onCheckedChange={(checked) => setRememberMe(checked as boolean)}
              />
              <label
                htmlFor="remember"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                {t('auth.rememberMe')}
              </label>
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" className="w-full mt-2" disabled={loading}>
              {loading ? t('common.loading') : t('auth.signIn')}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}