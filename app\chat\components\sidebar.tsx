'use client';

import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import { useConversation } from '../context/ConversationContext';
import { Plus, ChevronDown, PanelLeft } from 'lucide-react';
import * as Collapsible from '@radix-ui/react-collapsible';
import { motion, AnimatePresence } from 'framer-motion';
import { useState, useMemo, useEffect } from 'react';
import { SettingsModal } from '@/components/settings-modal';
import { EditConversationModal } from './edit-conversation-modal';
import { DeleteConversationModal } from './delete-conversation-modal';
import { LogoIcon } from '@/components/Logo';
import { useUI } from '../context/UIContext';

export interface Conversation {
  id: string;
  title: string;
  created_at: string;
}

interface ConversationGroup {
  label: string;
  conversations: Conversation[];
  key: string;
}

// 时间分组函数
const groupConversationsByTime = (conversations: Conversation[], t: any): ConversationGroup[] => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  const oneWeekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

  const groups: ConversationGroup[] = [
    { label: t('chat.timeGroups.today'), conversations: [], key: 'today' },
    { label: t('chat.timeGroups.yesterday'), conversations: [], key: 'yesterday' },
    { label: t('chat.timeGroups.lastWeek'), conversations: [], key: 'lastWeek' },
    { label: t('chat.timeGroups.earlier'), conversations: [], key: 'earlier' },
  ];

  conversations.forEach((conversation) => {
    const createdDate = new Date(conversation.created_at);

    if (createdDate >= today) {
      groups[0].conversations.push(conversation);
    } else if (createdDate >= yesterday) {
      groups[1].conversations.push(conversation);
    } else if (createdDate >= oneWeekAgo) {
      groups[2].conversations.push(conversation);
    } else {
      groups[3].conversations.push(conversation);
    }
  });

  // 只返回有对话的分组
  return groups.filter(group => group.conversations.length > 0);
};

export function Sidebar() {
  const { t } = useTranslation();
  const router = useRouter();
  const [mounted, setMounted] = useState(false)
  const { toggleSidebar } = useUI();
  const {
    conversations,
    activeConversationId,
    setActiveConversationId,
    createNewConversation,
    updateConversationTitle,
    deleteConversationById,
  } = useConversation();

  useEffect(() => {
    setMounted(true)
  }, [])

  // 折叠状态管理
  const [collapsedGroups, setCollapsedGroups] = useState<Set<string>>(new Set());

  // 分组对话
  const groupedConversations = useMemo(() =>
    groupConversationsByTime(conversations, t),
    [conversations, t]
  );

  const handleNewConversation = () => {
    router.push('/chat/new');
  };

  const toggleGroup = (groupKey: string) => {
    setCollapsedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupKey)) {
        newSet.delete(groupKey);
      } else {
        newSet.add(groupKey);
      }
      return newSet;
    });
  };

  // 删除会话
  const handleDelete = async (conversationId: string) => {
    try {
      await deleteConversationById(conversationId);
    } catch (error) {
      console.error('删除会话失败:', error);
    }
  };

  if (!mounted) {
    return null
  }

  return (
    <div
      className={`bg-sidebar md:border-r border-sidebar-border flex flex-col h-full w-full`}
    >
      {/* 顶部 Logo */}
      <div className="flex items-center justify-between px-3 py-3 border-b border-sidebar-border">
        <div className="flex items-center gap-2">
          <LogoIcon className="h-6 w-6" aria-label="NebulaMind Logo" />
          <span className="text-base font-semibold text-foreground select-none">NebulaMind</span>
        </div>
        <button
          className="inline-flex items-center justify-center h-7 w-7 rounded-md hover:bg-accent/50 text-muted-foreground hover:text-foreground transition-colors"
          onClick={toggleSidebar}
          aria-label="Collapse sidebar"
          title="折叠菜单"
        >
          <PanelLeft className="h-4 w-4" />
        </button>
      </div>

      {/* 新会话按钮 */}
      <div className="px-3 py-3 border-b border-sidebar-border">
        <button
          onClick={handleNewConversation}
          className="w-full flex items-center justify-center gap-2 px-4 py-2.5 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors duration-200 font-medium text-sm"
          aria-label={t('chat.newConversation')}
        >
          <Plus className="h-4 w-4" />
          {t('chat.newConversation')}
        </button>
      </div>

      {/* 分组对话列表 */}
      <div className="flex-1 overflow-y-auto py-2 px-3 scrollbar-hide">
        <div className="space-y-2">
          {groupedConversations.map((group) => (
            <Collapsible.Root
              key={group.key}
              open={!collapsedGroups.has(group.key)}
              onOpenChange={() => toggleGroup(group.key)}
            >
              {/* 分组标题 */}
              <Collapsible.Trigger asChild>
                <motion.button
                  className="flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors rounded-lg hover:bg-accent/30 group"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className="flex items-center gap-2">
                    <motion.div
                      animate={{
                        rotate: collapsedGroups.has(group.key) ? -90 : 0
                      }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronDown className="h-4 w-4" />
                    </motion.div>
                    {group.label}
                  </span>
                  <span className="text-xs bg-muted/50 px-2 py-1 rounded-full">
                    {group.conversations.length}
                  </span>
                </motion.button>
              </Collapsible.Trigger>

              {/* 可折叠内容 */}
              <Collapsible.Content forceMount asChild>
                <AnimatePresence>
                  {!collapsedGroups.has(group.key) && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{
                        duration: 0.3,
                        ease: [0.04, 0.62, 0.23, 0.98]
                      }}
                      className="overflow-hidden"
                    >
                      <div className="pl-6 pr-3 space-y-1.5 mt-2">
                        {group.conversations.map((conversation, index) => (
                          <motion.div
                            key={conversation.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{
                              delay: index * 0.05,
                              duration: 0.2
                            }}
                            className={`relative group rounded-lg transition-all duration-200 cursor-pointer ${activeConversationId === conversation.id
                              ? 'bg-accent/80 text-accent-foreground shadow-sm'
                              : 'hover:bg-accent/50'
                              }`}
                            onClick={() => router.push(`/chat/${conversation.id}`)}
                            whileHover={{ scale: 1.01, x: 4 }}
                            whileTap={{ scale: 0.99 }}
                          >
                            <div className="p-3 pr-12">
                              <div className="font-medium truncate text-sm leading-tight">
                                {conversation.title}
                              </div>
                              <div className="text-xs mt-1.5 text-muted-foreground/60">
                                {new Date(conversation.created_at).toLocaleString('zh-CN', {
                                  month: 'short',
                                  day: 'numeric',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </div>
                            </div>

                            {/* 操作按钮组 - 悬停时显示 */}
                            <motion.div
                              className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 bg-background/90 backdrop-blur-sm p-1 rounded-md border border-border/50 shadow-sm"
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 0, scale: 0.8 }}
                              whileHover={{ opacity: 1, scale: 1 }}
                              transition={{ duration: 0.15 }}
                            >
                              <div onClick={(e) => e.stopPropagation()}>
                                <EditConversationModal
                                  conversation={conversation}
                                  onUpdate={updateConversationTitle}
                                />
                              </div>
                              <div onClick={(e) => e.stopPropagation()}>
                                <DeleteConversationModal
                                  conversation={conversation}
                                  onDelete={handleDelete}
                                />
                              </div>
                            </motion.div>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </Collapsible.Content>
            </Collapsible.Root>
          ))}
        </div>
      </div>

      {/* 底部设置区域 */}
      <div className="border-t border-sidebar-border p-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-muted-foreground">{t('common.settings')}</span>
          <SettingsModal />
        </div>
      </div>
    </div>
  );
}