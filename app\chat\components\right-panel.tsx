'use client';

import { X } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { RightPanelState } from '@/app/chat/types/right-panel';
import { KnowledgeViewer } from './knowledge-viewer';
import WebResultsViewer from './web-results-viewer';

interface RightPanelProps {
  state: RightPanelState;
  onClose: () => void;
}

export function RightPanel({ state, onClose }: RightPanelProps) {
  const getPanelTitle = () => {
    switch (state.type) {
      case 'knowledge':
        return '知识库原文';
      case 'web':
        return '网页搜索结果';
      case 'report':
        return '生成报告';
      case 'code':
        return '代码执行';
      default:
        return '侧边栏';
    }
  };

  const renderContent = () => {
    switch (state.type) {
      case 'knowledge':
        return <KnowledgeViewer data={state.data?.type === 'knowledge' ? state.data.data : undefined} />;
      case 'web':
        return <WebResultsViewer data={state.data?.type === 'web' ? state.data.data : undefined} />;
      case 'report':
        return (
          <div className="p-6">
            <div className="text-center">
              <h3 className="text-lg font-medium mb-2">生成报告</h3>
              <p className="text-sm text-muted-foreground">报告功能开发中...</p>
            </div>
          </div>
        );
      case 'code':
        return (
          <div className="p-6">
            <div className="text-center">
              <h3 className="text-lg font-medium mb-2">代码执行</h3>
              <p className="text-sm text-muted-foreground">代码执行功能开发中...</p>
            </div>
          </div>
        );
      default:
        return (
          <div className="p-6">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">请选择要查看的内容</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="w-full bg-sidebar border-l border-sidebar-border flex flex-col h-full">
      <div className="p-4 border-b border-sidebar-border flex items-center justify-between">
        <h2 className="text-lg font-semibold">{getPanelTitle()}</h2>
        <Button
          variant="ghost"
          size="icon"
          onClick={onClose}
          className="h-8 w-8 rounded-full"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
      <div className="flex-1">
        {renderContent()}
      </div>
    </div>
  );
}