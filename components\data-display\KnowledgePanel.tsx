"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { X, ChevronLeft, ChevronRight, FileText, FileCode, Volume2 } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";

export interface KnowledgeItemMeta {
  file_name?: string;
  file_path?: string;
  file_size?: number;
  word_count?: number;
  chunk_order?: number;
  file_format?: string;
  content_text?: string;
  created_time?: string;
  modified_time?: string;
  start_ms?: number;
  end_ms?: number;
}

interface KnowledgePanelProps {
  items: KnowledgeItemMeta[];
  className?: string;
  onRequestSourcePreview?: (item: KnowledgeItemMeta, index: number) => void;
  onViewSource?: (items: KnowledgeItemMeta[]) => void;
  popoverProps?: React.ComponentProps<typeof PopoverContent>;
}

function getDisplayName(it: KnowledgeItemMeta) {
  if (it.file_name) return it.file_name;
  if (it.file_path) {
    try {
      const url = new URL(it.file_path);
      return decodeURIComponent(url.pathname.split("/").pop() || it.file_path);
    } catch {
      const segs = it.file_path.split("/");
      return decodeURIComponent(segs[segs.length - 1] || it.file_path);
    }
  }
  if (it.content_text) return it.content_text.slice(0, 16) + (it.content_text.length > 16 ? "…" : "");
  return "未知来源";
}

const KnowledgeBadge = React.forwardRef<HTMLDivElement, {
  items: KnowledgeItemMeta[];
  className?: string;
} & React.HTMLAttributes<HTMLDivElement>>(({ items, className, ...props }, ref) => {
  const list = Array.isArray(items) ? items : [];
  const total = list.length;
  if (!total) return null;

  return (
    <div
      ref={ref}
      role="button"
      aria-label={`查看引用的${total}条知识来源`}
      className={cn(
        "cursor-pointer inline-flex items-center justify-center rounded-md bg-primary/10 px-4 py-2 text-sm font-medium text-primary transition-colors hover:bg-primary/20 focus:outline-none focus:ring-2 focus:ring-primary/50",
        className
      )}
      {...props}
    >
      <span className="mr-2 h-4 w-4 text-primary">📚</span>
      <span>引用知识来源 ({total}条)</span>
    </div>
  );
});
KnowledgeBadge.displayName = "KnowledgeBadge";

const KnowledgeContent: React.FC<{
  items: KnowledgeItemMeta[];
  className?: string;
  onClose?: () => void;
  onRequestSourcePreview?: (item: KnowledgeItemMeta, index: number) => void;
  onViewSource?: (items: KnowledgeItemMeta[]) => void;
}> = ({ items, className = "", onClose, onRequestSourcePreview, onViewSource }) => {
  const list = Array.isArray(items) ? items : [];
  if (!list.length) return null;

  const [currentIndex, setCurrentIndex] = React.useState(0);
  const total = list.length;
  const currentItem = list[currentIndex];

  const prev = () => setCurrentIndex((i) => (i - 1 + total) % total);
  const next = () => setCurrentIndex((i) => (i + 1) % total);

  React.useEffect(() => {
    setCurrentIndex(0);
  }, [items]);

  if (!currentItem) return null;

  const isAudio = currentItem.file_format?.startsWith("audio/");
  const duration = (currentItem.end_ms ?? 0) - (currentItem.start_ms ?? 0);

  return (
    <div className={`flex flex-col bg-popover text-popover-foreground rounded-lg border border-border shadow-lg transition-all duration-200 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-border/50 bg-background/80">
        <div className="flex items-center gap-2 text-sm font-medium text-foreground">
          <FileText className="w-4 h-4 text-primary" />
          <span>引用内容</span>
        </div>
        <Button variant="ghost" size="icon" onClick={onClose} className="h-7 w-7 rounded-full text-muted-foreground hover:text-foreground hover:bg-muted transition-colors">
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* Content */}
      <div className="p-4 text-sm leading-relaxed max-h-72 overflow-y-auto scrollbar-thin scrollbar-thumb-muted/50 scrollbar-track-transparent">
        <div className="prose prose-sm prose-neutral max-w-none">
          {currentItem.content_text || "（无片段内容）"}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="px-4 pb-4 pt-2 space-y-2">
        {isAudio && (
          <Button 
            className="w-full transition-all duration-200 hover:shadow-md"
            onClick={() => onRequestSourcePreview?.(currentItem, currentIndex)}
          >
            <Volume2 className="w-4 h-4 mr-2" />
            收听音频片段 ({new Date(duration).toISOString().substr(14, 5)})
          </Button>
        )}
        
        <Button 
          variant="outline"
          className="w-full transition-all duration-200 hover:shadow-md"
          onClick={() => {
            onViewSource?.(list);
            onClose?.();
          }}
        >
          <FileText className="w-4 h-4 mr-2" />
          查看原文
        </Button>
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-border/50 bg-muted/50">
        <div className="flex items-center justify-between text-sm">
          {/* Source information */}
          <div className="flex items-center gap-1 flex-1 text-xs text-muted-foreground truncate" title={getDisplayName(currentItem)}>
            <FileCode className="w-3 h-3 flex-shrink-0" />
            <span className="truncate">{getDisplayName(currentItem)}</span>
          </div>
          
          {/* Pagination controls */}
          <div className="flex items-center gap-1 ml-3 bg-background/70 rounded-full px-2 py-0.5">
            <Button variant="ghost" size="icon" onClick={prev} disabled={total <= 1} className="h-6 w-6 rounded-full text-xs text-muted-foreground hover:text-foreground hover:bg-muted transition-colors">
              <ChevronLeft className="w-3 h-3" />
            </Button>
            <span className="font-mono text-xs text-muted-foreground tabular-nums w-8 text-center">
              {currentIndex + 1}/{total}
            </span>
            <Button variant="ghost" size="icon" onClick={next} disabled={total <= 1} className="h-6 w-6 rounded-full text-xs text-muted-foreground hover:text-foreground hover:bg-muted transition-colors">
              <ChevronRight className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export const KnowledgePanel: React.FC<KnowledgePanelProps> = ({ items, className, onRequestSourcePreview, onViewSource, popoverProps }) => {
  const list = Array.isArray(items) ? items : [];
  const total = list.length;
  if (!total) return null;

  const [open, setOpen] = React.useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <KnowledgeBadge items={items} className={className} />
      </PopoverTrigger>
      <PopoverContent
        className="w-96 p-0"
        sideOffset={8}
        collisionPadding={8}
        {...popoverProps}
      >
        <KnowledgeContent 
          items={items}
          onClose={() => setOpen(false)}
          onRequestSourcePreview={onRequestSourcePreview}
          onViewSource={onViewSource}
        />
      </PopoverContent>
    </Popover>
  );
};

export default KnowledgePanel;