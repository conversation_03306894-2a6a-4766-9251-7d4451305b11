'use client';

import { Button } from '@/components/ui/button';
import { StopCircle } from 'lucide-react';

type StopGenerationButtonProps = {
  onClick: () => void;
  className?: string;
};

export function StopGenerationButton({ onClick, className }: StopGenerationButtonProps) {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={onClick}
      className={className}
    >
      <StopCircle className="h-4 w-4" />
    </Button>
  );
}
