import React, { useState } from 'react';
import { Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';

interface TableDisplayProps {
  data: Array<Record<string, any>>;
  className?: string;
}

const PAGE_SIZE = 10;

export function TableDisplay({ data, className = '' }: TableDisplayProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [cachedColumns, setCachedColumns] = useState<string[]>([]);

  // 使用useEffect来更新列缓存，避免在数据流式更新时列频繁变化
  React.useEffect(() => {
    if (data.length > 0) {
      const newColumns = Object.keys(data[0]);
      // 只有当列有变化时才更新，避免不必要的重渲染
      if (JSON.stringify(newColumns) !== JSON.stringify(cachedColumns)) {
        setCachedColumns(newColumns);
      }
    } else if (cachedColumns.length > 0) {
      setCachedColumns([]);
    }
  }, [data, cachedColumns]);

  // 计算总页数和分页数据
  const { totalPages, paginatedData } = React.useMemo(() => {
    const total = Math.max(1, Math.ceil(data.length / PAGE_SIZE));
    const startIndex = (currentPage - 1) * PAGE_SIZE;
    return {
      totalPages: total,
      paginatedData: data.slice(startIndex, startIndex + PAGE_SIZE)
    };
  }, [data, currentPage]);

  // 计算列宽度，根据内容长度动态调整
  const getColumnWidth = (column: string) => {
    if (!data.length) return 'auto';

    // 计算该列的最大内容长度
    const maxLength = Math.max(
      column.length, // 表头长度
      ...data.slice(0, 50).map(row => String(row[column] ?? '').length) // 取前50行计算
    );

    // 根据内容长度设置最小宽度
    if (maxLength > 50) return 'minmax(200px, 1fr)';
    if (maxLength > 20) return 'minmax(150px, auto)';
    if (maxLength > 10) return 'minmax(120px, auto)';
    return 'minmax(100px, auto)';
  };

  // 格式化单元格内容
  const formatCellContent = (value: any) => {
    const str = String(value ?? '');
    // 如果内容太长，显示省略号并添加 tooltip
    if (str.length > 100) {
      return (
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="block truncate cursor-help">
              {str.substring(0, 100)}...
            </span>
          </TooltipTrigger>
          <TooltipContent side="top" className="max-w-xs break-words">
            {str}
          </TooltipContent>
        </Tooltip>
      );
    }
    return str;
  };

  // 如果没有数据或列，显示加载中或空状态
  if (data.length === 0 || cachedColumns.length === 0) {
    return <div className="p-4 text-muted-foreground">加载数据中...</div>;
  }

  return (
    <div className={`overflow-hidden rounded-lg border ${className}`}>
      <div className="overflow-x-auto">
        <table
          className="w-full caption-bottom text-sm"
          style={{
            tableLayout: 'fixed',
            minWidth: `${cachedColumns.length * 120}px`
          }}
        >
          <thead className="bg-muted/50">
            <tr className="border-b transition-colors">
              {cachedColumns.map((column) => (
                <th
                  key={column}
                  className="h-12 px-4 text-center align-middle font-semibold text-foreground whitespace-nowrap"
                  style={{
                    width: getColumnWidth(column),
                    minWidth: '100px'
                  }}
                >
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="truncate cursor-help">
                        {column}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="top">
                      {column}
                    </TooltipContent>
                  </Tooltip>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {paginatedData.map((row, rowIndex) => (
              <tr
                key={`${rowIndex}-${JSON.stringify(row)}`}
                className="border-b transition-colors hover:bg-muted/30 group"
              >
                {cachedColumns.map((column) => (
                  <td
                    key={`${rowIndex}-${column}`}
                    className="px-2 py-1 align-middle text-center"
                    style={{ minWidth: '100px' }}
                  >
                    <div className="break-words">
                      {formatCellContent(row[column])}
                    </div>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {totalPages > 1 && (
        <div className="flex items-center justify-between px-4 py-3 border-t bg-muted/20">
          <div className="text-sm text-muted-foreground">
            共 {data.length} 条记录，第 {currentPage} / {totalPages} 页
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
              disabled={currentPage === 1}
              className="px-3 py-1.5 text-sm rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none transition-colors"
            >
              上一页
            </button>
            <span className="text-sm text-muted-foreground px-2">
              {currentPage} / {totalPages}
            </span>
            <button
              onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
              disabled={currentPage === totalPages}
              className="px-3 py-1.5 text-sm rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none transition-colors"
            >
              下一页
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
