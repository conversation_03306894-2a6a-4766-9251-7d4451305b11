'use client';

import { useState, useRef, useEffect } from 'react';

interface ResizeHandleProps {
  direction: 'horizontal' | 'vertical';
  onResize: (delta: number) => void;
  className?: string;
  onDragChange?: (dragging: boolean) => void;
}

export function ResizeHandle({ direction, onResize, className = '', onDragChange }: ResizeHandleProps) {
  const [isDragging, setIsDragging] = useState(false);
  const startPositionRef = useRef(0);

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    onDragChange?.(true);
    startPositionRef.current = direction === 'horizontal' ? e.clientX : e.clientY;
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;
      
      const currentPosition = direction === 'horizontal' ? e.clientX : e.clientY;
      const delta = currentPosition - startPositionRef.current;
      
      onResize(delta);
      startPositionRef.current = currentPosition;
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      onDragChange?.(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = direction === 'horizontal' ? 'col-resize' : 'row-resize';
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isDragging, direction, onResize, onDragChange]);

  const baseClasses = direction === 'horizontal' 
    ? 'w-1 cursor-col-resize hover:bg-primary/20 transition-colors'
    : 'h-1 cursor-row-resize hover:bg-primary/20 transition-colors';

  return (
    <div
      className={`${baseClasses} ${className} ${isDragging ? 'bg-primary/30' : ''}`}
      onMouseDown={handleMouseDown}
    />
  );
}