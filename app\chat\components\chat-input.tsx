'use client';

import { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { StopGenerationButton } from './stop-generation-button';
import { cn } from '@/lib/utils';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  onStopGeneration?: () => void;
  placeholder?: string;
  disabled?: boolean;
  isLoading?: boolean;
  maxRows?: number;
}

export function ChatInput({ 
  onSendMessage, 
  onStopGeneration,
  placeholder, 
  disabled = false, 
  isLoading = false,
  maxRows = 6
}: ChatInputProps) {
  const { t } = useTranslation();
  const [inputValue, setInputValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 自动调整 textarea 高度
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const lineHeight = 24; // 大约的行高
      const maxHeight = lineHeight * maxRows;
      textarea.style.height = `${Math.min(scrollHeight, maxHeight)}px`;
    }
  }, [inputValue, maxRows]);

  const handleSendMessage = () => {
    if (inputValue.trim() === '') return;
    onSendMessage(inputValue);
    setInputValue('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!isLoading) {
        handleSendMessage();
      }
    }
  };

  const renderSendButton = () => {
    if (isLoading && onStopGeneration) {
      return (
        <StopGenerationButton 
          onClick={onStopGeneration}
          className="px-3 h-9 text-xs"
        />
      );
    }

    return (
      <button
        className={cn(
          "p-2 rounded-xl transition-all shadow-md inline-flex items-center justify-center",
          "disabled:opacity-50 disabled:cursor-not-allowed",
          inputValue.trim() && !disabled && !isLoading
            ? "bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-lg"
            : "bg-muted text-muted-foreground"
        )}
        onClick={handleSendMessage}
        disabled={!inputValue.trim() || disabled || isLoading}
        aria-label={t('common.send')}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2.5"
          strokeLinecap="round"
          strokeLinejoin="round"
          className={cn(
            "transition-transform duration-200",
            inputValue.trim() && !disabled && !isLoading && "hover:translate-x-0.5"
          )}
        >
          <path d="m22 2-7 20-4-9-9-4Z" />
          <path d="M22 2 11 13" />
        </svg>
      </button>
    );
  };

  return (
    <div className="border-t border-border/30 bg-background/90 backdrop-blur-md sticky bottom-0 pb-4 pt-2">
      <div className="max-w-3xl mx-auto px-4">
        <div className={cn(
          "bg-background/80 border border-border/50 rounded-2xl shadow-lg backdrop-blur-sm transition-all duration-200",
          isFocused && "border-primary/50 shadow-xl",
          "p-2"
        )}>
          <div className="relative">
            <div className="relative">
              <textarea
                ref={textareaRef}
                rows={1}
                className={cn(
                  "w-full min-h-[60px] px-4 py-3.5 pr-16 rounded-xl bg-transparent text-foreground",
                  "placeholder:text-muted-foreground/60 focus:outline-none resize-none scrollbar-hide",
                  "transition-all duration-200",
                  isLoading && "opacity-70"
                )}
                placeholder={isLoading ? t('chat.generating', 'AI 正在生成回复...') : (placeholder || t('chat.typeMessage', '输入消息...'))}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyPress}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                disabled={disabled || isLoading}
              />
              
              <div className="absolute right-3 bottom-3 flex items-center gap-2">
                {renderSendButton()}
              </div>
            </div>
          </div>
          
          <div className="px-3 mt-1.5 flex items-center justify-between">
            <div className="flex items-center text-xs text-muted-foreground">
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                width="14" 
                height="14" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                className="mr-1 flex-shrink-0"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 16v-4"></path>
                <path d="M12 8h.01"></path>
              </svg>
              <span>{t('chat.disclaimer', '回答可能不准确')}</span>
            </div>
            {!isLoading && (
              <div className="text-xs text-muted-foreground">
                <kbd className="px-1.5 py-0.5 bg-muted rounded text-[10px] font-mono">Enter</kbd> 发送 / 
                <kbd className="px-1.5 py-0.5 bg-muted rounded text-[10px] font-mono ml-1">Shift+Enter</kbd> 换行
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}