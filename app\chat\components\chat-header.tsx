"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { useConversation } from "@/app/chat/context/ConversationContext";
import { useUI } from "@/app/chat/context/UIContext";
import { LogoIcon } from "@/components/Logo";
import { PanelLeft, Plus } from "lucide-react";

interface ChatHeaderProps {
  showTitle?: boolean;
  disableNew?: boolean;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({
  showTitle = true,
  disableNew = false,
}) => {
  const router = useRouter();
  const { toggleSidebar, sidebarVisible } = useUI();
  const { conversations, activeConversationId } = useConversation();

  const activeTitle = React.useMemo(() => {
    if (!showTitle) return "";
    const conv = conversations.find((c) => c.id === activeConversationId);
    return conv?.title ?? "";
  }, [showTitle, conversations, activeConversationId]);

  return (
    <div className="h-12 flex items-center gap-3 bg-background px-3">
      {/* 折叠状态：显示 Logo（可选）、展开按钮与新对话按钮 */}
      {!sidebarVisible && (
        <>
          <button
            onClick={toggleSidebar}
            className="inline-flex items-center justify-center h-8 w-8 rounded-md hover:bg-accent/50 text-muted-foreground hover:text-foreground transition-colors"
            aria-label="Expand sidebar"
            title="展开菜单"
          >
            <PanelLeft className="h-4 w-4 rotate-180" />
          </button>

          <button
            onClick={() => router.push("/chat/new")}
            disabled={disableNew}
            className={`inline-flex items-center gap-2 px-3 h-8 rounded-md border transition-colors text-sm ${
              disableNew
                ? "opacity-50 cursor-not-allowed border-border text-muted-foreground"
                : "border-primary text-primary hover:bg-primary/10"
            }`}
            aria-label="新对话"
            title={disableNew ? "新对话（当前不可用）" : "新对话"}
          >
            <Plus className="h-4 w-4" />
            <span>新对话</span>
          </button>
        </>
      )}

      {showTitle && (
        <div className="ml-2 truncate text-sm text-foreground/90 max-w-[40%]">
          {activeTitle || ""}
        </div>
      )}

      <div className="flex-1" />
    </div>
  );
};
