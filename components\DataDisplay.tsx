import React, { useState, useMemo, useEffect } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { TableDisplay } from './data-display/TableDisplay';
import { ChartDisplay } from './data-display/ChartDisplay';
import { SqlDisplay } from './data-display/SqlDisplay';

export interface DataDisplayProps {
  chartData?: any;
  sql?: string;
  tableData?: Array<Record<string, any>>;
}

interface TabItem {
  id: string;
  label: string;
  disabled: boolean;
}

const DataDisplay: React.FC<DataDisplayProps> = ({
  chartData,
  sql,
  tableData = [],
}) => {
  // 计算可用的标签页
  const availableTabs = React.useMemo(() => {
    const hasChart = chartData && Object.keys(chartData).length > 0;
    const hasTable = tableData && tableData.length > 0;
    const hasSql = !!sql;
    
    const tabs = [
      hasTable && 'table',
      hasChart && 'chart',
      hasSql && 'sql'
    ].filter(Boolean) as string[];
    
    return {
      tabs,
      hasData: tabs.length > 0
    };
  }, [chartData, sql, tableData]);
  
  // 确保 activeTab 始终是一个有效的标签页
  const [activeTab, setActiveTab] = React.useState<string>(() => {
    return availableTabs.tabs[0] || 'table';
  });
  
  // 当可用的标签页变化时，确保 activeTab 是有效的
  React.useEffect(() => {
    if (availableTabs.tabs.length > 0 && !availableTabs.tabs.includes(activeTab)) {
      setActiveTab(availableTabs.tabs[0]);
    }
  }, [availableTabs.tabs, activeTab]);
  
  // 如果没有数据，则不渲染组件
  if (!availableTabs.hasData) {
    return null;
  }

  const tabs = React.useMemo((): TabItem[] => {
    return availableTabs.tabs.map(tab => {
      switch(tab) {
        case 'table':
          return { id: 'table', label: '数据表格', disabled: false };
        case 'chart':
          return { id: 'chart', label: '图表', disabled: false };
        case 'sql':
          return { id: 'sql', label: 'SQL 查询', disabled: false };
        default:
          return { id: tab, label: tab, disabled: false };
      }
    });
  }, [availableTabs.tabs]);

  if (tabs.length === 0) {
    return null;
  }

  return (
    <div className="mt-4 rounded-lg border border-border overflow-hidden" key={`data-display-${Date.now()}`}>
      <Tabs 
        defaultValue={tabs[0]?.id}
        className="w-full"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList className="w-full justify-start rounded-t-lg border-b bg-muted/20 p-2">
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.id}
              value={tab.id}
              className="relative rounded-md px-4 py-4 text-sm font-medium transition-colors hover:bg-muted/50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
            >
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>

        {tabs.some(tab => tab.id === 'chart') && (
          <TabsContent value="chart" className="m-0">
            <ChartDisplay chartData={chartData} />
          </TabsContent>
        )}

        {tabs.some(tab => tab.id === 'table') && (
          <TabsContent value="table" className="m-0">
            <TableDisplay data={tableData} />
          </TabsContent>
        )}

        {tabs.some(tab => tab.id === 'sql') && (
          <TabsContent value="sql" className="m-0">
            <SqlDisplay sql={sql || ''} />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};

export default DataDisplay;
