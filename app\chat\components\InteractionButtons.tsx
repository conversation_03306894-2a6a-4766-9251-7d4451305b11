import { But<PERSON> } from "@/components/ui/button";
import { Refresh<PERSON>w, Copy, ThumbsUp, ThumbsDown, Check } from "lucide-react";

interface InteractionButtonsProps {
  onRetry?: () => void;
  onCopy?: () => void;
  onLike?: () => void;
  onDislike?: () => void;
  feedback?: 1 | 2 | null;
  copied?: boolean;
}

export const InteractionButtons: React.FC<InteractionButtonsProps> = ({
  onRetry,
  onCopy,
  onLike,
  onDislike,
  feedback,
  copied,
}) => {
  return (
    <div className="flex gap-1">
      <Button
        variant="ghost"
        size="icon"
        className="rounded-full"
        aria-label="重新发起"
        onClick={onRetry}
      >
        <RefreshCw className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        className="rounded-full"
        aria-label="复制"
        onClick={onCopy}
      >
        {copied ? (
          <Check className="h-4 w-4 text-green-600" />
        ) : (
          <Copy className="h-4 w-4" />
        )}
      </Button>
      <Button
        variant="ghost"
        size="icon"
        className="rounded-full"
        aria-label="点赞"
        onClick={onLike}
      >
        <ThumbsUp
          className={`h-4 w-4 ${feedback === 1 ? "text-primary" : ""}`}
        />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        className="rounded-full"
        aria-label="点踩"
        onClick={onDislike}
      >
        <ThumbsDown
          className={`h-4 w-4 ${feedback === 2 ? "text-red-500" : ""}`}
        />
      </Button>
    </div>
  );
};
